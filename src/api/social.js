
import fetch from 'utils/fetch'
import Qs from 'qs'

export default {
    /**
     * 拉取诊所医保配置信息
     * <AUTHOR>
     * @date 2020-09-15
     * @returns {Promise}
     */
    async fetchSocialConfig() {
        const res = await fetch({
            url: `/api/v2/shebao/configs`,
            method: 'GET'
        })
        return res.data
    },
    /**
     * 拉取诊所进销存配置信息
     * <AUTHOR>
     * @date 2020-09-15
     * @returns {Promise}
     */
    async fetchInventoryConfig() {
        const res = await fetch({
            url: `/api/v2/supervision/shebao/stock/config`,
            method: 'GET'
        })
        return res.data
    },
    /**
     * 更新医保配置信息
     * <AUTHOR>
     * @date 2020-05-07
     * @param {Object} data 配置信息
     * @returns {Promise}
     */
    async updateSocialConfig(data) {
        const res = await fetch({
            url: `/api/v2/shebao/configs`,
            method: 'POST',
            data
        })
        return res.data
    },

    /**
     * @desc 修改社保异动提醒设置
     * <AUTHOR>
     * @date 2020/12/08 16:44:33
     * @params
     * @return
     */
    async updateSocialWarningConfig(data) {
        const res = await fetch.put('/api/v2/shebao/configs/warning', data);
        return res.data;
    },
    /**
     * @desc 总部下获取子店社保配置信息
     * <AUTHOR>
     * @date 2025/1/8 下午7:15
     */
    async isOpenSocialChainAdmin() {
        const res = await fetch.get('/api/v2/shebao/configs/check-status');
        return res.data;
    },

    /**
     * 智能对码
     * <AUTHOR>
     * @date 2020-09-15
     * @param {Object} data 入参数据
     * @returns {Promise}
     */
    smartMatch(data) {
        return fetch({
            url: `/api/v2/shebao/chengdu/code/smart_match_easy`,
            method: 'POST',
            data
        })
    },

    /**
     * @desc 根据诊断获取医保诊断编码
     * <AUTHOR> Yang
     * @date 2020-12-09 11:56:38
     * @params
     * @return
     */
    async fetchDiagnosisSocialCode(diseasesName) {
        const res = await fetch({
            url: '/api/v2/shebao/diseases/code',
            method: 'post',
            data: {
                diseasesName,
            },
        })
        return res.data;
    },
    /**
     * 获取人员就诊记录信息
     * <AUTHOR>
     * @date 2021-02-03
     * @param {String} personalCode 人员编码
     * @returns {Promise}
     */
    async fetchMedicalRecords(personalCode) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/medical-record',
            method: 'GET',
            params: { personal_code: personalCode }
        })
        return res.data;
    },
    /**
     * 获取就诊记录上传信息
     * <AUTHOR>
     * @date 2021-02-03
     * @param {String} personalCode 人员编码
     * @returns {Promise}
     */
    async fetchMedicalRecordUploadInfo(personalCode) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/medical-record/upload-info',
            method: 'GET',
            params: { personal_code: personalCode }
        })
        return res.data;
    },
    /**
     * 获取医保的就诊记录 - 待上传明细
     * <AUTHOR>
     * @date 2020-10-12
     * @param {Object} params 查询入参
     * @returns {Promise}
     */
    async fetchMedicalRecordDetailNeedSync(personalCode) {
        const res = await fetch({
            url: `/api/v2/shebao/hangzhou/medical-record/need-sync-detail-info`,
            method: 'GET',
            params: { personalCode },
        })
        return res.data;
    },

    /**
     * @desc 包含医保信息的诊断搜索接口
     * <AUTHOR>
     * @date 2021-07-09 17:39:22
     */
    async searchSocialDiagnosis({keyword, type}) {
        const res = await fetch({
            url: '/api/v2/shebao/diagnosis/search',
            method: 'get',
            params: {
                keyword,
                type,
            },
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    // 医院管家诊断搜索
    async hospitalSearchSocialDiagnosis(params) {
        const res = await fetch({
            url: '/api/v2/shebao/diagnosis/hospital/search',
            method: 'get',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res?.data;
    },

    // 医院管家手术搜索
    async hospitalSearchSocialOperation(params) {
        const res = await fetch({
            url: '/api/v2/shebao/operation/search',
            method: 'get',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res?.data;
    },

    /**
     * @desc 包含医保信息的诊断搜索接口
     * <AUTHOR>
     * @date 2021-07-09 17:39:22
     */
    async querySocialCodeByDiagnosis(diagnosisInfos) {
        const res = await fetch({
            url: '/api/v2/shebao/diagnosis/query',
            method: 'post',
            data: {
                diagnosisInfos
            }

        });
        return res.data;
    },
    /**
     * @desc 获取就诊记录新老人头次数
     * <AUTHOR>
     * @date 2021-09-09 16:53:24
     * @param {*} personalCode
     */
    async querySocialPersonalTimes(personalCode) {
        const res = await fetch({
            url: '/api/v2/shebao-stat/hangzhou/medical-record/personal-times',
            method: 'GET',
            params: { personalCode },
        })
        return res.data;
    },

    /* 杭州医保 - 签到签退需求 */
    /**
     * 根据名字查询系统里医生的基本信息
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} params 查询参数
     * @returns {Promise<Object>}
     */
    async doctorQueryIds(params) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/doctor/query-ids',
            method: 'GET',
            params,
            // params: {
            //     keyword: '',//关键词
            // }
        })
        return res.data;
    },
    /**
     * 医生签到
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    async doctorSignIn(data) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/doctor/sign-in',
            method: 'PUT',
            data,
            // data: {
            //     idCardNo: '',//身份证号
            //     name: '',//名字
            // },
        })
        return res.data;
    },
    /**
     * 医生未签到设置为已经提醒过了
     * <AUTHOR>
     * @date 2022-02-22
     * @param doctorId
     * @returns {Promise<Object>}
     */
    async markDoctorSignIn(doctorId, data) {
        const res = await fetch({
            url: `/api/v2/shebao/hangzhou/management/doctor/mark-not-signin-as-reminded/${doctorId}`,
            method: 'PUT',
            data,
        })
        return res.data;
    },
    /**
     * 检查医生是否存在有效签到
     * <AUTHOR>
     * @date 2022-02-22
     * @param doctorId
     * @returns {Promise<Object>}
     */
    async checkDoctorSignInfo(doctorId) {
        const res = await fetch({
            url: `/api/v2/shebao/hangzhou/management/doctor/validate-sign-in/${doctorId}`,
            method: 'get',
        })
        return res && res.data
    },
    /**
     * 医生签到信息列表
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} params 查询参数
     * @returns {Promise<Object>}
     */
    async doctorSignInfoList(params) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/doctor/sign-info-list',
            method: 'GET',
            params,
            // params: {
            //     beginDate: '',//开始日期
            //     endDate: '',//结束日期
            //     idCardNo: '',//身份证号码
            //     limit: '',
            //     offset: '',
            // },
        })
        return res.data;
    },
    /**
     * 医生签退
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    async doctorSignOut(data) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/doctor/sign-out',
            method: 'PUT',
            data,
            // data: {
            //     idCardNo: '',//身份证号
            //     name: '',//名字
            // },
        })
        return res.data;
    },
    /**
     * 根据名字查询系统里患者的基本信息
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} params 查询参数
     * @returns {Promise<Object>}
     */
    async patientQueryIds(params) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/patient/query-ids',
            method: 'GET',
            params,
            // params: {
            //     keyword: '',//关键词
            // }
        })
        return res.data;
    },
    /**
     * 患者签到
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    async patientSignIn(data) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/patient/sign-in',
            method: 'PUT',
            data,
            // data: {
            //     idCardNo: '',//身份证号
            //     name: '',//名字
            // },
        })
        return res.data;
    },
    /**
     * 患者签到信息列表
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} params 查询参数
     * @returns {Promise<Object>}
     */
    async patientSignInfoList(params) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/patient/sign-info-list',
            method: 'GET',
            params,
            // params: {
            //     beginDate: '',//开始日期
            //     endDate: '',//结束日期
            //     idCardNo: '',//身份证号码
            //     limit: '',
            //     offset: '',
            // },
        })
        return res.data;
    },
    /**
     * 患者签退
     * <AUTHOR>
     * @date 2022-01-17
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    async patientSignOut(data) {
        const res = await fetch({
            url: '/api/v2/shebao/hangzhou/management/patient/sign-out',
            method: 'PUT',
            data,
            // data: {
            //     idCardNo: '',//身份证号
            //     name: '',//名字
            // },
        })
        return res.data;
    },

    /**
     * @desc 外配处方开发票
     * <AUTHOR>
     * @date 2022-02-28 19:27:09
     */
    async invoiceOrder(id, data) {
        const res = await fetch({
            url: `/api/v2/shebao/external-outpatient/invoice/${id}`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 外诊处方 冲红发票
     * <AUTHOR>
     * @date 2022-03-02 14:38:15
     * @params
     * @return
     */
    async chongHongInvoice(id, data) {
        const res = await fetch({
            url: `/api/v2/shebao/external-outpatient/invoice/${id}/chonghong`,
            method: 'put',
            data,
        })
        return res.data;
    },

    /**
     * @desc 外诊处方 作废发票
     * <AUTHOR>
     * @date 2022-03-02 14:41:05
     * @params
     * @return
     */
    async destroyInvoice(id, data) {
        const res = await fetch({
            url: `/api/v2/shebao/external-outpatient/invoice/${id}/destroy`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * 创建收费二维码
     * <AUTHOR>
     * @date 2022-05-09
     * @param {String} taskId 结算任务单id
     * @returns {Promise<Object>}
     */
    async createChargeQrCode(taskId) {
        const res = await fetch({
            url: `/api/v2/shebao/tasks/qr-code/${taskId}`,
            method: 'GET',
        });
        return res.data;
    },

    /**
     * 扫码收费入账
     * <AUTHOR>
     * @date 2022-05-09
     * @param {String} taskId 结算任务单id
     * @param {Object} data 入参数据
     * @returns {Promise<Object>}
     */
    async qrCodeChargePaid(taskId, data) {
        const res = await fetch({
            url: `/api/v2/shebao/tasks/qr-code/paid/${taskId}`,
            method: 'PUT',
            data,
        });
        return res.data;
    },

    /**
     * 创建退费二维码
     * <AUTHOR>
     * @date 2022-05-09
     * @param {String} taskId 结算任务单id
     * @returns {Promise<Object>}
     */
    async createRefundQrCode(taskId) {
        const res = await fetch({
            url: `/api/v2/shebao/tasks/qr-code/refund/${taskId}`,
            method: 'GET',
        });
        return res.data;
    },

    /**
     * 扫码退费入账
     * <AUTHOR>
     * @date 2022-05-09
     * @param {String} taskId 结算任务单id
     * @returns {Promise<Object>}
     */
    async qrCodeRefundPaid(taskId) {
        const res = await fetch({
            url: `/api/v2/shebao/tasks/qr-code/refund/${taskId}`,
            method: 'PUT',
        });
        return res.data;
    },

    /**
     * @desc 获取入院办理状态
     * <AUTHOR>
     * @date 2023-02-23
     */
    async fetchSocialInHospitalStatus(patientOrderId) {
        const res = await fetch({
            url: `/api/v2/shebao/national/management/patientorders/${patientOrderId}`,
            method: 'GET'
        })
        return res.data
    },

    //通过chargeSheetId查收费信息
    async getSocialSettleInfo(chargeSheetId, reqClinicId) {
        const res = await fetch({
            url: `/api/v2/shebao/national/charge/payment/${chargeSheetId}`,
            method: 'get',
            params: {
                reqClinicId,
            }
        });
        return res?.data;
    },

    /**
     * 搜索手术操作
     * @param params {Object} 参数
     * @param params.cis-his-type {string} 诊所类型
     * @param params.keyword {string} 搜索词
     * @return {Promise<Object>}
     * 返回值 eg:
     * {
     *   "operationInfos": [
     *     {
     *       "clinicalCode": "string",
     *       "clinicalName": "string",
     *       "code": "string",
     *       "name": "string"
     *     }
     *   ]
     * }
     */
    async searchOperation(params) {
        const res = await fetch({
            url: '/api/v2/shebao/dict-operation/search',
            method: 'GET',
            params,
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });

        return res.data || {};
    },

    /**
     * 手术查询接口
     * @param data
     * @return {Promise<{Object}>}
     * 参数 eg:
     * {
     *   "shebaoDictOperationList": [
     *     {
     *       "code": "string",
     *       "name": "string"
     *     }
     *   ]
     * }
     * 返回值 eg:
     * {
     *   "operationInfos": [
     *     {
     *       "clinicalCode": "string",
     *       "clinicalName": "string",
     *       "code": "string",
     *       "name": "string"
     *     }
     *   ]
     * }
     */
    async queryOperation(data) {
        const res = await fetch({
            url: '/api/v2/shebao/dict-operation/query',
            method: 'POST',
            data,
        });

        return res.data || {};
    },

    // 查询患者已经登记的自付比例信息-所有goods
    async getPatientSelfPay(patientOrderId, data) {
        const res = await fetch({
            url: `/api/v2/shebao/national/special-needs/self-pay/outpatient/query/${patientOrderId}`,
            method: 'POST',
            data,
        });

        return res.data || {};
    },

    // 登记患者已开goods自费比例
    async registerPatientSelfPay(patientOrderId, data) {
        const res = await fetch({
            url: `/api/v2/shebao/national/special-needs/self-pay/outpatient/register/${patientOrderId}`,
            method: 'POST',
            data,
        });

        return res.data || {};
    },

    // 门诊处-编辑自付比例后查询上次登记信息
    async getPatientSelfpayProp(patientOrderId, data) {
        const res = await fetch({
            url: `/api/v2/shebao/national/special-needs/self-pay/outpatient/edited-query/${patientOrderId}`,
            method: 'POST',
            data,
        });

        return res.data || {};
    },

    // 消费登记
    async registerSpecialNeeds(chargeSheetId, data) {
        const res = await fetch({
            url: `/api/v2/shebao/national/special-needs/${chargeSheetId}`,
            method: 'POST',
            data,
        });

        return res.data || {};
    },

    // 重试入账
    async retryRefund(taskId) {
        const res = await fetch({
            url: `/api/v2/shebao/generic/charge/retry-refund/${taskId}`,
            method: 'POST',
        });

        return res.data || {};
    },

    /**
     * @desc 获取社保task list
     */
    async fetchShebaoTaskList(params) {
        const res = await fetch({
            url: `/api/v2/shebao/national/charge/tasks`,
            method: 'GET',
            params,
        });

        return res.data || {};
    },
}
