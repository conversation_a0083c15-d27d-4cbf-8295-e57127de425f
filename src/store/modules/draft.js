/**
 * @desc 草稿模块
 * <AUTHOR>
 * @date 2018/06/19 15:19:10
 */

import Store from 'utils/localStorage-handler';
import Auth from './auth';
import GoodsInAPI from 'api/goods/stock-in';
import GoodsTransAPI from 'api/goods/stock-trans';
import {
    GOODS_IN_STATUS,
    GOODS_OUT_STATUS,
    GOODS_CHECK_STATUS,
} from 'views/inventory/constant';
import { TRANS_STATUS_ENUM } from 'views/inventory/goods-trans/trans-config';
import { APPLY_STATUS_ENUM } from 'views/inventory/goods-apply/apply-config';
import GoodsOutAPI from 'api/goods/stock-out';
import GoodsApplyAPI from 'api/goods/stock-apply';
import GoodsCheckAPI from 'api/goods/check';

// 挂号草稿
const DRAFT_REGISTRATION_NEW = 'draft__registration_news';
const DRAFT_REGISTRATION = 'draft__registration';

// 门诊草稿
const DRAFT_OUTPATIENT_NEWS = 'draft__outpatient_news';
const DRAFT_OUTPATIENT = 'draft__outpatients';

// 收费草稿
const DRAFT_CASHIER_NEW = 'draft__cashier_news';
const DRAFT_CASHIER = 'draft__cashiers';

// 治疗理疗开单草稿
const DRAFT_TREATMENT_NEW = 'draft__treatment_news';
const DRAFT_TREATMENT = 'draft__treatments';

// 检查单草稿
const DRAFT_INSPECT = 'draft__inspect';

// 咨询单草稿
const DRAFT_CONSULT = 'draft__consult';

// 采购草稿
const DRAFT_GOODS_PURCHASE = 'draft_goods_purchase';
// 入库草稿
const DRAFT_GOODS_IN = 'draft_goods_in';
// 出库草稿-报损出库、科室消耗、其他出库
const DRAFT_GOODS_OUT = 'draft_goods_out';
// 调拨草稿
const DRAFT_GOODS_TRANS = 'draft_goods_trans';
// 盘点草稿
const DRAFT_GOODS_CHECK = 'draft_goods_check';
// 领用草稿
const DRAFT_GOODS_APPLY = 'draft_goods_apply';

const KEY = Auth.state.currentClinic ? `${Auth.state.currentClinic.clinicId}_${Auth.state.currentClinic.userId}` : '';


/**
 * @desc 新增或者修改草稿
 * <AUTHOR> Yang
 * @date 2020-12-22 14:16:56
 * @params list<Array> 操作的草稿列表
 * @params draft<Object> 新增或修改项目
 */
function addOrUpdateDraftHandle(list, draft) {
    if (!draft.draftId) {
        draft.draftId = `${Date.now()}`;
    }
    draft.draftId = `${draft.draftId}`;

    const inCaches = list.filter((r) => {
        return draft.draftId === `${r.draftId}`;
    });
    if (inCaches.length) {
        list.forEach((r) => {
            if (draft.draftId === `${r.draftId}`) {
                Object.assign(r, draft);
            }
        });
    } else {
        list.unshift(draft);
    }
    /**
     * @desc 最多存50个草稿，先进先出
     * <AUTHOR>
     * @date 2020/02/27 14:37:39
     */
    if (list.length > 50) {
        list.length = 50;
    }
}

/**
 * @desc 删除草稿
 * <AUTHOR> Yang
 * @date 2020-12-22 14:28:37
 */
function deleteDraftHandle(list, draftId) {
    let index = null;
    list.forEach((it, idx) => {
        if (`${it.draftId}` === `${draftId}`) {
            index = idx;
        }
    });
    if (index !== null) {
        list.splice(index, 1);
    }
}
/**
 * @desc 处理草稿数据
 * <AUTHOR>
 * @date 2020-12-22 14:16:56
 * @params list<Array> 操作的草稿列表
 * @params status<number> 草稿状态
 */
function formatDraftData(list, status) {
    return list.map((item) => ({
        ...item,
        statusName: '草稿',
        status,
        createdDate: item.created || item.createdDate || '',
        lastModifiedDate: item.lastModified || item.lastModifiedDate || '',
        isCloud: Boolean(!item.draftId && item.id),
    })).sort((a, b) => {
        const aDate = new Date(a.lastModifiedDate).getTime();
        const bDate = new Date(b.lastModifiedDate).getTime();
        return bDate - aDate;
    });
}
const draft = {
    state: {
        registrationNewItem: Store.getObj(DRAFT_REGISTRATION_NEW, KEY, true) || null,
        registrations: Store.getObj(DRAFT_REGISTRATION, KEY, true) || null,

        outpatientNews: Store.getObj(DRAFT_OUTPATIENT_NEWS, KEY, true) || [], // 新增患者草稿
        outpatients: Store.getObj(DRAFT_OUTPATIENT, KEY, true) || [],

        cashierNews: Store.getObj(DRAFT_CASHIER_NEW, KEY, true) || [],
        cashiers: Store.getObj(DRAFT_CASHIER, KEY, true) || [],

        treatmentNews: Store.getObj(DRAFT_TREATMENT_NEW, KEY, true) || [],
        treatments: Store.getObj(DRAFT_TREATMENT, KEY, true) || [],

        inspects: Store.getObj(DRAFT_INSPECT, KEY, true) || [],

        consults: Store.getObj(DRAFT_CONSULT, KEY, true) || [],

        // 本地草稿都有draftId（时间戳）, 云草稿有id（后端生成），本地草稿和云草稿都有id时，以最后修改的为准
        goodsPurchase: Store.getObj(DRAFT_GOODS_PURCHASE, KEY, true) || [],
        goodsIn: Store.getObj(DRAFT_GOODS_IN, KEY, true) || [],
        goodsOut: Store.getObj(DRAFT_GOODS_OUT, KEY, true) || [],
        goodsTrans: Store.getObj(DRAFT_GOODS_TRANS, KEY, true) || [],
        goodsCheck: Store.getObj(DRAFT_GOODS_CHECK, KEY, true) || [],
        goodsApply: Store.getObj(DRAFT_GOODS_APPLY, KEY, true) || [],
        cloudGoodsIn: [],// 入库单云草稿数据
        cloudGoodsOut: [],// 出库单云草稿数据
        cloudGoodsTrans: [],// 调拨单云草稿数据
        cloudGoodsApply: [],// 领用单云草稿数据
        cloudGoodsCheck: [],// 盘点单云草稿数据
    },
    getters: {
        draftGoodsIn: (state) => {
            const list = [...state.goodsIn, ...state.cloudGoodsIn];

            return formatDraftData(list, GOODS_IN_STATUS.DRAFT);
        },
        draftGoodsOut: (state) => {
            const list = [...state.goodsOut, ...state.cloudGoodsOut];

            return formatDraftData(list, GOODS_OUT_STATUS.DRAFT);
        },
        draftGoodsTrans: (state) => {
            const list = [...state.goodsTrans, ...state.cloudGoodsTrans];

            return formatDraftData(list, TRANS_STATUS_ENUM.DRAFT);
        },
        draftGoodsCheck: (state) => {
            const list = [...state.goodsCheck, ...state.cloudGoodsCheck];

            return formatDraftData(list, GOODS_CHECK_STATUS.DRAFT);
        },
        draftGoodsApply: (state) => {
            const list = [...state.goodsApply, ...state.cloudGoodsApply];

            return formatDraftData(list, APPLY_STATUS_ENUM.DRAFT);
        },
    },
    mutations: {
        SET_REGISTRATION_NEW: (state, newItem) => {
            state.registrationNewItem = newItem;
            Store.setObj(DRAFT_REGISTRATION_NEW, KEY, state.registrationNewItem);
        },
        SET_OUTPATIENT_NEWS: (state, record) => {
            addOrUpdateDraftHandle(state.outpatientNews, record);
            Store.setObj(DRAFT_OUTPATIENT_NEWS, KEY, state.outpatientNews);
        },
        CLEAR_OUTPATIENT_NEWS: (state, draftId) => {
            deleteDraftHandle(state.outpatientNews, draftId);
            Store.setObj(DRAFT_OUTPATIENT_NEWS, KEY, state.outpatientNews);
        },
        SET_OUTPATIENTS: (state, record) => {
            addOrUpdateDraftHandle(state.outpatients, record);
            Store.setObj(DRAFT_OUTPATIENT, KEY, state.outpatients);
        },
        CLEAR_OUTPATIENTS: (state, draftId) => {
            deleteDraftHandle(state.outpatients, draftId);
            Store.setObj(DRAFT_OUTPATIENT, KEY, state.outpatients);
        },
        SET_CASHIER_NEWS: (state, record) => {
            addOrUpdateDraftHandle(state.cashierNews, record);
            Store.setObj(DRAFT_CASHIER_NEW, KEY, state.cashierNews);
        },
        CLEAR_CASHIER_NEW: (state, draftId) => {
            deleteDraftHandle(state.cashierNews, draftId);
            Store.setObj(DRAFT_CASHIER_NEW, KEY, state.cashierNews);
        },
        CLEAR_CASHIER_NEW_ALL: (state) => {
            state.cashierNews = [];
            Store.setObj(DRAFT_CASHIER_NEW, KEY, state.cashierNews);
        },
        SET_CASHIERS: (state, record) => {
            addOrUpdateDraftHandle(state.cashiers, record);
            Store.setObj(DRAFT_CASHIER, KEY, state.cashiers);
        },
        CLEAR_CASHIERS: (state, draftId) => {
            deleteDraftHandle(state.cashiers, draftId);
            Store.setObj(DRAFT_CASHIER, KEY, state.cashiers);
        },
        SET_TREATMENT_NEWS: (state, record) => {
            addOrUpdateDraftHandle(state.treatmentNews, record);
            Store.setObj(DRAFT_TREATMENT_NEW, KEY, state.treatmentNews);
        },
        CLEAR_TREATMENT_NEW: (state, draftId) => {
            deleteDraftHandle(state.treatmentNews, draftId);
            Store.setObj(DRAFT_TREATMENT_NEW, KEY, state.treatmentNews);
        },
        SET_TREATMENTS: (state, record) => {
            addOrUpdateDraftHandle(state.treatments, record);
            Store.setObj(DRAFT_TREATMENT, KEY, state.treatments);
        },
        CLEAR_TREATMENTS: (state, draftId) => {
            deleteDraftHandle(state.treatments, draftId);
            Store.setObj(DRAFT_TREATMENT, KEY, state.treatments);
        },
        SET_INSPECTS: (state, record) => {
            addOrUpdateDraftHandle(state.inspects, record);
            Store.setObj(DRAFT_INSPECT, KEY, state.inspects);
        },
        CLEAR_INSPECTS: (state, draftId) => {
            deleteDraftHandle(state.inspects, draftId);
            Store.setObj(DRAFT_INSPECT, KEY, state.inspects);
        },
        SET_CONSULT: (state, record) => {
            addOrUpdateDraftHandle(state.consults, record);
            Store.setObj(DRAFT_CONSULT, KEY, state.consults);
        },
        CLEAR_CONSULT: (state, draftId) => {
            deleteDraftHandle(state.consults, draftId);
            Store.setObj(DRAFT_CONSULT, KEY, state.consults);
        },
        SET_GOODS_PURCHASE: (state, record) => {
            addOrUpdateDraftHandle(state.goodsPurchase, record);
            state.goodsPurchase.sort((a, b) => {
                return new Date(a.lastModifiedDate).getTime() < new Date(b.lastModifiedDate).getTime();
            });
            Store.setObj(DRAFT_GOODS_PURCHASE, KEY, state.goodsPurchase);
        },
        CLEAR_GOODS_PURCHASE: (state, draftId) => {
            deleteDraftHandle(state.goodsPurchase, draftId);
            Store.setObj(DRAFT_GOODS_PURCHASE, KEY, state.goodsPurchase);
        },
        SET_GOODS_IN: (state, record) => {
            addOrUpdateDraftHandle(state.goodsIn, record);
            state.goodsIn.sort((a, b) => {
                const aDate = new Date(a.lastModifiedDate || a.lastModified).getTime();
                const bDate = new Date(b.lastModifiedDate || b.lastModified).getTime();
                return bDate - aDate;
            });
            Store.setObj(DRAFT_GOODS_IN, KEY, state.goodsIn);
        },
        CLEAR_GOODS_IN: (state, draftId) => {
            deleteDraftHandle(state.goodsIn, draftId);
            Store.setObj(DRAFT_GOODS_IN, KEY, state.goodsIn);
        },
        SET_GOODS_OUT: (state, record) => {
            addOrUpdateDraftHandle(state.goodsOut, record);
            state.goodsOut.sort((a, b) => {
                const aDate = new Date(a.lastModifiedDate || a.lastModified).getTime();
                const bDate = new Date(b.lastModifiedDate || b.lastModified).getTime();
                return bDate - aDate;
            });
            Store.setObj(DRAFT_GOODS_OUT, KEY, state.goodsOut);
        },
        CLEAR_GOODS_OUT: (state, draftId) => {
            deleteDraftHandle(state.goodsOut, draftId);
            Store.setObj(DRAFT_GOODS_OUT, KEY, state.goodsOut);
        },
        SET_GOODS_TRANS: (state, record) => {
            addOrUpdateDraftHandle(state.goodsTrans, record);
            state.goodsTrans.sort((a, b) => {
                const aDate = new Date(a.lastModifiedDate || a.lastModified).getTime();
                const bDate = new Date(b.lastModifiedDate || b.lastModified).getTime();
                return bDate - aDate;
            });
            Store.setObj(DRAFT_GOODS_TRANS, KEY, state.goodsTrans);
        },
        CLEAR_GOODS_TRANS: (state, draftId) => {
            deleteDraftHandle(state.goodsTrans, draftId);
            Store.setObj(DRAFT_GOODS_TRANS, KEY, state.goodsTrans);
        },
        SET_GOODS_CHECK: (state, record) => {
            addOrUpdateDraftHandle(state.goodsCheck, record);
            state.goodsCheck.sort((a, b) => {
                const aDate = new Date(a.lastModifiedDate || a.lastModified).getTime();
                const bDate = new Date(b.lastModifiedDate || b.lastModified).getTime();
                return bDate - aDate;
            });
            Store.setObj(DRAFT_GOODS_CHECK, KEY, state.goodsCheck);
        },
        CLEAR_GOODS_CHECK: (state, draftId) => {
            deleteDraftHandle(state.goodsCheck, draftId);
            Store.setObj(DRAFT_GOODS_CHECK, KEY, state.goodsCheck);
        },
        SET_GOODS_APPLY: (state, record) => {
            addOrUpdateDraftHandle(state.goodsApply, record);
            state.goodsApply.sort((a, b) => {
                const aDate = new Date(a.lastModifiedDate || a.lastModified).getTime();
                const bDate = new Date(b.lastModifiedDate || b.lastModified).getTime();
                return bDate - aDate;
            });
            Store.setObj(DRAFT_GOODS_APPLY, KEY, state.goodsApply);
        },
        CLEAR_GOODS_APPLY: (state, draftId) => {
            deleteDraftHandle(state.goodsApply, draftId);
            Store.setObj(DRAFT_GOODS_APPLY, KEY, state.goodsApply);
        },
        SET_CLOUD_GOODS_IN: (state, record) => {
            state.cloudGoodsIn = record;
        },
        SET_CLOUD_GOODS_OUT: (state, record) => {
            state.cloudGoodsOut = record;
        },
        SET_CLOUD_GOODS_TRANS: (state, record) => {
            state.cloudGoodsTrans = record;
        },
        SET_CLOUD_GOODS_CHECK: (state, record) => {
            state.cloudGoodsCheck = record;
        },
        SET_CLOUD_GOODS_APPLY: (state, record) => {
            state.cloudGoodsApply = record;
        },
    },

    actions: {
        // ================================== 本地草稿方法 ==================================
        SetDraft({ commit }, data) {
            switch (data.key) {
                case 'registration':
                    commit('SET_REGISTRATION_NEW', data.record);
                    break;
                case 'outpatientNews':
                    commit('SET_OUTPATIENT_NEWS', data.record);
                    break;
                case 'outpatients':
                    commit('SET_OUTPATIENTS', data.record);
                    break;
                case 'cashier':
                    commit('SET_CASHIER_NEWS', data.record);
                    break;
                case 'cashiers':
                    commit('SET_CASHIERS', data.record);
                    break;
                case 'treatment':
                    commit('SET_TREATMENT_NEWS', data.record);
                    break;
                case 'treatments':
                    commit('SET_TREATMENTS', data.record);
                    break;
                case 'inspects':
                    commit('SET_INSPECTS', data.record);
                    break;
                case 'consult':
                    commit('SET_CONSULT', data.record);
                    break;
                case 'goods-purchase':
                    commit('SET_GOODS_PURCHASE', data.record);
                    break;
                case 'goods-in':
                    commit('SET_GOODS_IN', data.record);
                    break;
                case 'goods-out':
                    commit('SET_GOODS_OUT', data.record);
                    break;
                case 'goods-trans':
                    commit('SET_GOODS_TRANS', data.record);
                    break;
                case 'goods-check':
                    commit('SET_GOODS_CHECK', data.record);
                    break;
                case 'goods-apply':
                    commit('SET_GOODS_APPLY', data.record);
                    break;
                default:
                    console.error('Invalid Key');
            }
        },

        ClearDraft({ commit }, data) {
            switch (data.key) {
                case 'registration':
                    commit('SET_REGISTRATION_NEW', null);
                    break;
                case 'outpatientNews':
                    commit('CLEAR_OUTPATIENT_NEWS', data.draftId);
                    break;
                case 'outpatients':
                    commit('CLEAR_OUTPATIENTS', data.draftId);
                    break;
                case 'cashier':
                    commit('CLEAR_CASHIER_NEW', data.draftId);
                    break;
                case 'cashierAll':
                    commit('CLEAR_CASHIER_NEW_ALL');
                    break;
                case 'cashiers':
                    commit('CLEAR_CASHIERS', data.draftId);
                    break;
                case 'treatment':
                    commit('CLEAR_TREATMENT_NEW', data.draftId);
                    break;
                case 'treatments':
                    commit('CLEAR_TREATMENTS', data.record);
                    break;
                case 'inspects':
                    commit('CLEAR_INSPECTS', data.record);
                    break;
                case 'consult':
                    commit('CLEAR_CONSULT', data.draftId);
                    break;
                case 'goods-purchase':
                    commit('CLEAR_GOODS_PURCHASE', data.draftId);
                    break;
                case 'goods-in':
                    commit('CLEAR_GOODS_IN', data.draftId);
                    break;
                case 'goods-out':
                    commit('CLEAR_GOODS_OUT', data.draftId);
                    break;
                case 'goods-trans':
                    commit('CLEAR_GOODS_TRANS', data.draftId);
                    break;
                case 'goods-check':
                    commit('CLEAR_GOODS_CHECK', data.draftId);
                    break;
                case 'goods-apply':
                    commit('CLEAR_GOODS_APPLY', data.draftId);
                    break;
                default:
                    console.error('Invalid Key');
            }
        },
        // ================================== 云草稿方法 ==================================
        async fetchGoodsCloudDraft({ commit }, key) {
            try {
                // 获取失败不影响后续操作
                switch (key) {
                    case 'goods-check': {
                        const { data } = await GoodsCheckAPI.getGoodsCheckDraftList();
                        commit('SET_CLOUD_GOODS_CHECK', data.rows || []);
                        break;
                    }
                    case 'goods-apply': {
                        const { data } = await GoodsApplyAPI.getGoodsApplyDraftList();
                        commit('SET_CLOUD_GOODS_APPLY', data.rows || []);
                        break;
                    }
                    case 'goods-out': {
                        const { data } = await GoodsOutAPI.getGoodsOutDraftList();
                        commit('SET_CLOUD_GOODS_OUT', data.rows || []);
                        break;
                    }
                    case 'goods-in': {
                        const { data } = await GoodsInAPI.getGoodsInDraftList();
                        commit('SET_CLOUD_GOODS_IN', data.rows || []);
                        break;
                    }
                    case 'goods-trans': {
                        const { data } = await GoodsTransAPI.getGoodsTransDraftList();
                        commit('SET_CLOUD_GOODS_TRANS', data.rows || []);
                        break;
                    }
                    default:
                        console.error('Invalid Key');
                }
            } catch (e) {
                console.error(e);
            }
        },

        async getGoodsCloudDraftDetail(_, record) {
            let result = null;
            switch (record.key) {
                case 'goods-check': {
                    const { data } = await GoodsCheckAPI.getGoodsCheckDraftDetail(record.draftId);
                    result = data;
                    break;
                }
                case 'goods-apply': {
                    const { data } = await GoodsApplyAPI.getGoodsApplyDraftDetail(record.draftId);
                    result = data;
                    break;
                }
                case 'goods-out': {
                    const { data } = await GoodsOutAPI.getGoodsOutDraftDetail(record.draftId);
                    result = data;
                    break;
                }
                case 'goods-in': {
                    const { data } = await GoodsInAPI.getGoodsInDraftDetail(record.draftId);
                    result = data;
                    break;
                }
                case 'goods-trans':{
                    const { data } = await GoodsTransAPI.getGoodsTransDraftDetail(record.draftId);
                    result = data;
                    break;
                }
                default:
                    console.error('Invalid Key');
            }
            return result;
        },

        async saveGoodsCloudDraft({ dispatch }, data, isRefresh = true) {
            console.log('saveGoodsCloudDraft', data);

            const recordExcludeList = {
                ...data.record,
            };
            const recordExcludeOrder = {
                ...data.record,
            };
            delete recordExcludeList.list;
            delete recordExcludeOrder.order;
            // 先保存为本地草稿，不在cache里面做
            dispatch('SetDraft', {
                key: data.key,
                record: recordExcludeList,
            });
            if (data.key === 'goods-in') {
                if (data.record?.id) {
                    await GoodsInAPI.updateGoodsInDraft(data.record.id, recordExcludeOrder);
                } else {
                    await GoodsInAPI.createGoodsInDraft(recordExcludeOrder);
                }
                // 创建云草稿后，清除本地草稿
                dispatch('ClearDraft', {
                    key: data.key,
                    draftId: data.record.draftId,
                });
            }
            if (data.key === 'goods-out') {
                if (data.record?.id) {
                    await GoodsOutAPI.updateGoodsOutDraft(data.record.id, recordExcludeOrder);
                } else {
                    await GoodsOutAPI.createGoodsOutDraft(recordExcludeOrder);
                }
                // 创建云草稿后，清除本地草稿
                dispatch('ClearDraft', {
                    key: data.key,
                    draftId: data.record.draftId,
                });
            }
            if (data.key === 'goods-apply') {
                if (data.record?.id) {
                    await GoodsApplyAPI.updateGoodsApplyDraft(data.record.id, recordExcludeOrder);
                } else {
                    await GoodsApplyAPI.createGoodsApplyDraft(recordExcludeOrder);
                }
                // 创建云草稿后，清除本地草稿
                dispatch('ClearDraft', {
                    key: data.key,
                    draftId: data.record.draftId,
                });
            }
            if (data.key === 'goods-check') {
                if (data.record?.id) {
                    await GoodsCheckAPI.updateGoodsCheckDraft(data.record.id, recordExcludeOrder);
                } else {
                    await GoodsCheckAPI.createGoodsCheckDraft(recordExcludeOrder);
                }
                // 创建云草稿后，清除本地草稿
                dispatch('ClearDraft', {
                    key: data.key,
                    draftId: data.record.draftId,
                });
            }
            if (data.key === 'goods-trans') {
                if (data.record?.id) {
                    await GoodsTransAPI.updateGoodsTransDraft(data.record.id, recordExcludeOrder);
                } else {
                    await GoodsTransAPI.createGoodsTransDraft(recordExcludeOrder);
                }
                // 创建云草稿后，清除本地草稿
                dispatch('ClearDraft', {
                    key: data.key,
                    draftId: data.record.draftId,
                });
            }
            // 保存后重新获取草稿列表
            if (isRefresh) {
                await dispatch('fetchGoodsCloudDraft', data.key);
            }
        },

        async deleteGoodsCloudDraft({ dispatch }, data, isRefresh = true) {
            console.log('deleteGoodsCloudDraft', data);
            if (data.key === 'goods-in') {
                await GoodsInAPI.deleteGoodsInDraft(data.draftId);
            }
            if (data.key === 'goods-out') {
                await GoodsOutAPI.deleteGoodsOutDraft(data.draftId);
            }
            if (data.key === 'goods-apply') {
                await GoodsApplyAPI.deleteGoodsApplyDraft(data.draftId);
            }
            if (data.key === 'goods-check') {
                await GoodsCheckAPI.deleteGoodsCheckDraft(data.draftId);
            }
            if (data.key === 'goods-trans') {
                await GoodsTransAPI.deleteGoodsTransDraft(data.draftId);
            }
            // 删除后重新获取草稿列表
            if (isRefresh) {
                await dispatch('fetchGoodsCloudDraft', data.key);
            }
        },
    },
};
export default draft;
