import { isJSON } from '@/common/utils.js';
import {
    AttributesEnum, ExtensionTypeEnum, WidgetTypeEnum, WidgetTypeNameEnum,
} from '@abc-emr-editor/constants';
import { parseExtensionValue } from '@abc-emr-editor/tools';
import {
    formatDentistry2Text,
} from '@/common/utils/format-diagnosis';
import { MedicalRecordKeyLabelObj } from '@/common/constants/outpatient.js';
import {
    ToothChildMap, ToothQuadrant,
} from '@/common/constants/outpatient';


export const CoverKeyArr = [
    'symptomTime',
];

export const ClearKeyArr = [
    'symptomTime',
];

/**
 * @desc 既往史类别字段
 * <AUTHOR>
 * @date 2022-07-21 08:59:53
 */
export const PastHistoryTypeArr = [
    'pastHistory', // 既往史
    'familyHistory', //家族史
    'personalHistory', //个人史
    'obstetricalHistory', //月经婚孕史
    'epidemiologicalHistory', //流行病史
    'allergicHistory', // 过敏史
];

// 数组类病历字段，可包含 toothNos
export const SpecialKeyArr = [
    'disposals',
    'treatmentPlans',
    'dentistryExaminations',
    'auxiliaryExaminations',
    'extendDiagnosisInfos',
];

/**
 * @desc 所有产品线的病历字段
 * @desc 这个里面加配置找JasonYang讨论下，影响点有点多
 * <AUTHOR>
 * @date 2023-03-06 14:48:50
 */
export const AllMRKey = [
    {
        key: 'chiefComplaint', label: '主诉',
    },
    {
        key: 'symptomTime', label: '发病日期',
    },
    {
        key: 'presentHistory', label: '现病史', abridgeLabel: '现病',
    },
    {
        key: 'pastHistory', label: '既往史', abridgeLabel: '既往',
    },
    {
        key: 'wearGlassesHistory', abel: '戴镜史', abridgeLabel: '戴镜',
    },
    {
        key: 'familyHistory', label: '家族史', abridgeLabel: '家族',
    },
    {
        key: 'personalHistory', label: '个人史', abridgeLabel: '个人',
    },
    {
        key: 'birthHistory', label: '出生史', abridgeLabel: '出生',
    },
    {
        key: 'obstetricalHistory', label: '月经婚育史', abridgeLabel: '婚育',
    },
    {
        key: 'epidemiologicalHistory', label: '流行病史', abridgeLabel: '流病',
    },
    {
        key: 'physicalExamination', label: '体格检查', abridgeLabel: '体查',
    },
    {
        key: 'dentistryExaminations', label: '口腔检查', abridgeLabel: '检查',
    },
    {
        key: 'chineseExamination', label: '望闻切诊',
    },
    {
        key: 'tongue', label: '舌象',
    },
    {
        key: 'pulse', label: '脉象',
    },
    {
        key: 'oralExamination', label: '口腔检查', abridgeLabel: '口腔',
    },
    {
        key: 'eyeExamination', label: '眼部检查', abridgeLabel: '眼部',
    },
    {
        key: 'auxiliaryExaminations', label: '辅助检查', abridgeLabel: '辅查',
    },
    // 放在诊断前
    {
        key: 'allergicHistory', label: '过敏史', abridgeLabel: '过敏',
    },
    {
        key: 'extendDiagnosisInfos', label: '诊断',
    },
    {
        key: 'treatmentPlans', label: '计划',
    },
    {
        key: 'disposals', label: '处置',
    },
    {
        key: 'syndrome', label: '辨证',
    },
    {
        key: 'syndromes', label: '辨证', // 辨证v2
    },
    {
        key: 'syndromeTreatment', label: '辨证论治',
    },
    {
        key: 'therapy', label: '治法',
    },
    {
        key: 'chinesePrescription', label: '方药',
    },
    {
        key: 'target', label: '目标',
    },
    {
        key: 'prognosis', label: '预后',
    },
];

function formatRangeStr(range) {
    if (!Array.isArray(range)) return '';
    if (range[0] === range[1]) return range[0];
    return range.join('~');
}

// 月经婚育史 转成str
export function getObstetricalHistoryStr(list, customFormatRangeStr = null, id) {
    return list.map((it) => {
        if (typeof it === 'object') {
            if (it.type === 'pregnant') {
                const pregnant = `孕 ${it.pregnantCount || 0}`;
                const birth = `产 ${it.birthCount || 0}`;
                return `<span class="pregnant">${pregnant} ${birth}</span>`;
            }
            if (it.type === 'menstruation') {
                let $str = '';
                if (it.menopauseAge) {
                    $str += `${it.menopauseAge || ''}`;
                } else {
                    $str += `LMP ${it.menopauseDate || ''}`;
                }
                const menstruationDays = (customFormatRangeStr || formatRangeStr)(it.menstruationDays);
                const menstrualCycle = (customFormatRangeStr || formatRangeStr)(it.menstrualCycle);
                return `<span
                          class="menstruation"
                          ${AttributesEnum.ID}="${id}"
                          ${AttributesEnum.ExtensionType}="${ExtensionTypeEnum.MENSTRUATION}"
                          ${AttributesEnum.ExtensionValue}="${parseExtensionValue(it)}"
                          ${AttributesEnum.WidgetName}="月经史"
                          ${AttributesEnum.WidgetType}="${WidgetTypeEnum.MedicalFormulas}"
                          ${AttributesEnum.WidgetTypeName}="${WidgetTypeNameEnum[WidgetTypeEnum.MedicalFormulas]}"
                        >
                            ${it.menophaniaAge || ''}
                            <span>
                                <span>${menstruationDays}</span>
                                <span class="frasl"></span>
                                <span>${menstrualCycle}</span>
                            </span>
                            ${$str}
                        </span>`;
            }
            return '';
        }
        return it;
    }).join('，');
}

// 格式化 月经婚育史
export function formatObstetricalHistory2Str(obstetricalHistory) {
    if (!obstetricalHistory) return '';
    if (isJSON(obstetricalHistory)) {
        return getObstetricalHistoryStr(JSON.parse(obstetricalHistory));
    }

    return obstetricalHistory;
}

export function getEpidemiologicalHistoryStr(currentObj) {
    let str = '';
    const {
        suspiciousList = [],
        symptomList,
    } = currentObj;

    symptomList.forEach((item) => {
        str += item.label;
        if (item.isSuspicious) {
            if (suspiciousList && suspiciousList.length) {
                str += `${suspiciousList.map((it) => {
                    return `${it.label}（${it.value}）`;
                }).join('，')}`;
                const hasActive = suspiciousList.some((it) => it.value === '有');
                if (hasActive) {
                    str += '，需密切关注和引导新冠肺炎排查';
                }
                str += '。';
            } else {
                str += item.label ? '：' : '';
            }
        } else {
            // 兼容老数据
            if (item.value === 1) {
                str += '（是）；';
            } else if (item.value === 0) {
                str += '（否）；';
            } else if (item.value) {
                if (item.value === '是' && item.selectedOptions && item.selectedOptions.length > 0) {
                    if (item.label.includes('有任一症状即选择“是”')) {
                        str += `（有${item.selectedOptions.join('、')}症状）；`;
                    } else if (item.label.includes('旅行史或居住史')) {
                        str += `（有${item.selectedOptions.join('、')}旅居史）；`;
                    } else if (item.label.includes('接触史')) {
                        str += `（有${item.selectedOptions.join('、')}接触史）；`;
                    } else if (item.label.includes('有任一项既选“是”')) {
                        str += `（从事${item.selectedOptions.join('、')}相关工作）；`;
                    } else {
                        str += `（${item.value}）；`;
                    }
                } else {
                    str += `（${item.value}）；`;
                }
            } else if (str && !item.value) {
                str += str.match(/[;；]$/) ? '' : '；';
            }
        }
    });
    return str;
}

/**
 * @desc 格式化流行病史
 * <AUTHOR> Yang
 * @date 2020-12-09 17:16:32
 */
export function formatEpidemiologicalHistory2Str(epidemiologicalHistory) {
    if (!epidemiologicalHistory) return '';

    if (isJSON(epidemiologicalHistory)) {
        let currentObj = {
            patientChecked: false,
            attendantChecked: false,
            suspiciousList: [],
            symptomList: [],
        };
        const valObj = JSON.parse(epidemiologicalHistory);
        if (Array.isArray(valObj)) {
            currentObj.symptomList = valObj;
        } else {
            currentObj = valObj;
        }
        return getEpidemiologicalHistoryStr(currentObj);
    }

    return epidemiologicalHistory;
}

const positionsStrObj = {
    'top-left': 1,
    'top-right': 2,
    'bottom-right': 3,
    'bottom-left': 4,
};

function _trans2Str(list) {
    const _list = list.map((item) => {
        let str = item;
        if (typeof item === 'object') {
            str = '';
            item.positions.forEach((pos) => {
                pos.dataNo.forEach((no) => {
                    str += `${str ? ' ' : ''}#${positionsStrObj[pos.position]}${no}`;
                });
            });
            str += ` ${item.describes.join(' ')}`;
        }
        return str.trim();
    });
    return _list.join('，');
}

function _trans2Html(list) {
    const _list = list.map((item) => {
        let str = item;
        if (typeof item === 'object') {
            str = '';
            item.positions.forEach((pos) => {
                str += `<span class="${pos.position}">${pos.dataNo.join(' ')} </span>`;
            });
            str += ` ${item.describes.join(' ')}`;
        }
        return str.trim();
    });
    return _list.join('，');
}

/**
 * @desc 格式化口腔检查
 * <AUTHOR> Yang
 * @date 2020-10-15 08:42:35
 * @params for2String: true 是否转成 string 格式 类似#11, 默认转换成html
 */
export function formatOralExamination2Html(oralExamination, for2String = false) {
    if (!oralExamination) return '';
    if (isJSON(oralExamination)) {
        const _list = JSON.parse(oralExamination);
        if (for2String) {
            return _trans2Str(_list);
        }
        return _trans2Html(_list);

    }
    return oralExamination;

}

export function getToothNosInfo(arr) {
    arr = arr || [];
    const topLeft = [];
    const bottomLeft = [];
    const topRight = [];
    const bottomRight = [];
    arr.forEach((it) => {
        if (ToothQuadrant.TOP_LEFT.indexOf(it) > -1) {
            topLeft.push(it % 10);
        } else if (ToothQuadrant.TOP_RIGHT.indexOf(it) > -1) {
            topRight.push(it % 10);
        } else if (ToothQuadrant.BOTTOM_LEFT.indexOf(it) > -1) {
            bottomLeft.push(it % 10);
        } else if (ToothQuadrant.BOTTOM_RIGHT.indexOf(it) > -1) {
            bottomRight.push(it % 10);
        }
    });
    let isChildTooth = false;
    // FDI牙位表示法，大于48代表乳牙
    if (Math.max(...arr) > 48) {
        isChildTooth = true;
    }
    const topLength = topLeft.length + topRight.length;
    const bottomLength = bottomLeft.length + bottomRight.length;
    const isTopAll = bottomLength === 0 && (isChildTooth ? topLength === 10 : topLength === 16);
    const isBottomAll = topLength === 0 && (isChildTooth ? bottomLength === 10 : bottomLength === 16);

    const isAll = isChildTooth ? arr.length === 20 : arr.length === 32;

    let maxQuadrant = topLeft;
    if (topRight.length > maxQuadrant.length) {
        maxQuadrant = topRight;
    }
    if (bottomLeft.length > maxQuadrant.length) {
        maxQuadrant = bottomLeft;
    }
    if (bottomRight.length > maxQuadrant.length) {
        maxQuadrant = bottomRight;
    }

    let maxQuadrantWidth = 42;
    const length = maxQuadrant.length > 2 ? maxQuadrant.length : 2;
    // 单边单个字宽度8.5；整个上下半区*2
    let numWidth = 8;
    if (isChildTooth) {
        numWidth = 10;
    }
    maxQuadrantWidth = length * numWidth * 2 + numWidth;

    return {
        isAll, // 全口
        isTopAll, // 上半口
        isBottomAll, // 下半口
        isChildTooth,
        maxQuadrant,
        topLeft,
        bottomLeft,
        topRight,
        bottomRight,
        maxQuadrantWidth, // 最长牙位宽度
    };
}

export function formatToothNos2Html(toothNos, customWidth, id) {
    if (!toothNos) return '';
    const {
        isChildTooth,
        topLeft,
        bottomLeft,
        topRight,
        bottomRight,
        maxQuadrantWidth,
    } = getToothNosInfo(toothNos);

    let _topLeft = topLeft.sort((a,b) => b - a).slice();
    let _bottomLeft = bottomLeft.sort((a,b) => b - a).slice();
    let _topRight = topRight.sort((a,b) => a - b).slice();
    let _bottomRight = bottomRight.sort((a,b) => a - b).slice();

    if (isChildTooth) {
        _topLeft = _topLeft.map((it) => ToothChildMap[it]);
        _bottomLeft = _bottomLeft.map((it) => ToothChildMap[it]);
        _topRight = _topRight.map((it) => ToothChildMap[it]);
        _bottomRight = _bottomRight.map((it) => ToothChildMap[it]);
    }
    return `
        <span
             class="global-tooth-selected-quadrant"
             ${AttributesEnum.ID}="${id}"
             ${AttributesEnum.ExtensionType}="${ExtensionTypeEnum.TOOTH}"
             ${AttributesEnum.ExtensionValue}="${parseExtensionValue(toothNos)}"
             ${AttributesEnum.WidgetName}="牙位图"
             ${AttributesEnum.WidgetType}="${WidgetTypeEnum.MedicalFormulas}"
             ${AttributesEnum.WidgetTypeName}="${WidgetTypeNameEnum[WidgetTypeEnum.MedicalFormulas]}"
         >
            <span class="top-tooth" style="width: ${customWidth ? `${customWidth}px` : `${maxQuadrantWidth}px`}">
                <span class="left-tooth">${_topLeft.join('')}</span>
                <span class="right-tooth">${_topRight.join('')}</span>
            </span>
            <span class="bottom-tooth" style="width: ${customWidth ? `${customWidth}px` : `${maxQuadrantWidth}px`}">
                <span class="left-tooth">${_bottomLeft.join('')}</span>
                <span class="right-tooth">${_bottomRight.join('')}</span>
            </span>
        </span>
    `;
}

export function formatDentistry2Html(arr) {
    if (!arr) return '';
    let str = '';
    arr.forEach((item) => {
        if (item) {
            let toothNosHtml = '';
            if (item.toothNos && item.toothNos.length) {
                toothNosHtml += formatToothNos2Html(item.toothNos);
            }

            if (Array.isArray(item.value)) {
                if (item.value.length) {
                    str += `<div>${toothNosHtml}`;
                    str += item.value.map((it) => it.name).join('，');
                    str += '</div>';
                }
            } else {
                if (item.value) {
                    str += `<div>${toothNosHtml}${item.value}</div>`;
                }
            }
        }
    });
    return str;
}

export function formatEyeExamination2Html(eyeExamination) {
    if (!eyeExamination) return '';
    const _arr = [];
    const {
        items = [],
    } = eyeExamination;
    items.forEach((item) => {
        let str = '';
        if (item.rightEyeValue || item.leftEyeValue) {
            str += `${item.name}：`;
        }
        if (item.rightEyeValue) {
            const semi = item.leftEyeValue ? '；' : '';
            str += `右眼（OD）${item.rightEyeValue}${semi}`;
        }
        if (item.leftEyeValue) {
            str += `左眼（OS）${item.leftEyeValue}`;
        }
        if (str) {
            _arr.push(str);
        }
    });
    return _arr.join('<br/>');
}

/**
 * @desc 病历key对应不同的转换函数
 * <AUTHOR>
 * @date 2023-03-06 14:24:33
 */
export const specialMRKey2StrMap = {
    'obstetricalHistory': formatObstetricalHistory2Str,
    'epidemiologicalHistory': formatEpidemiologicalHistory2Str,
    'oralExamination': formatOralExamination2Html,
    'auxiliaryExaminations': formatDentistry2Html,
    'extendDiagnosisInfos': formatDentistry2Text,
    'dentistryExaminations': formatDentistry2Html,
    'treatmentPlans': formatDentistry2Html,
    'disposals': formatDentistry2Html,
    'eyeExamination': formatEyeExamination2Html,
};

export function getMRStructList(medicalRecord, doctorAdviceInForm = true) {
    // 兼容导入的门诊数据病历为 null
    const mrObj = medicalRecord || {};
    const allKeys = doctorAdviceInForm ? AllMRKey : [
        ...AllMRKey,
        {
            key: 'doctorAdvice',
            label: '医嘱建议',
        },
    ];
    return allKeys.map((it) => {
        let htmlText = mrObj[it.key];
        if (specialMRKey2StrMap[it.key]) {
            htmlText = specialMRKey2StrMap[it.key](htmlText);
        }
        if (htmlText) {
            const label = `${it.abridgeLabel || it.label || MedicalRecordKeyLabelObj[it.key]}：`;
            return {
                key: it.key,
                label,
                htmlText,
            };
        }
        return null;
    }).filter((item) => item);
}

export function medicalRecordHasValue(keyStr, data) {
    if (!data) return false;

    let htmlText = data;
    if (specialMRKey2StrMap[keyStr]) {
        htmlText = specialMRKey2StrMap[keyStr](htmlText);
    }
    return !!htmlText;
}

export function getMedicalRecordStruct(data, switchSetting = {}, doctorAdviceInForm) {
    const target = {
        ...switchSetting,
    };
    const allKeys = doctorAdviceInForm ? AllMRKey : [
        ...AllMRKey,
        {
            key: 'doctorAdvice',
            label: '医嘱建议',
        },
    ];
    allKeys.forEach((it) => {
        if (medicalRecordHasValue(it.key, data[it.key])) {
            target[it.key] = 1;
        }
    });
    // 默认都打开主诉、诊断展示
    target.chiefComplaint = 1;
    target.diagnosis = 1;
    return target;
}

/**
 * @desc 更新newData中相应的插入字段
 * <AUTHOR>
 * @date 2022-07-21 12:05:36
 * @param {Array} structKey  需要遍历的结构
 * @param {object} originData  被插入的病历结构
 * @param {object} newData 待插入的病历
 */
export function appendMedicalRecordHandler(structKey, originData, newData, isCoverAll = false) {

    if (!structKey || structKey.length === 0) return;

    structKey.forEach((key) => {

        const oldVal = originData[key];
        const newVal = newData[key];

        /** 老值没有 不存在冲突 插入直接不用管 */
        if (!medicalRecordHasValue(key, oldVal)) {
            return false;
        }

        /** 新值没有，直接用老值覆盖 不能清空已有的老值 */
        if (!medicalRecordHasValue(key, newVal)) {
            newData[key] = oldVal;
            return false;
        }

        /** 新老值 都存在，需要做插入规则 */

        /**  CoverKeyArr 直接用新值 */
        if (CoverKeyArr.indexOf(key) > -1) {
            newData[key] = newVal;
            return false;
        }

        /** 既往史类别字段 调用模板不覆盖已有值, 直接用老值 */
        if (!isCoverAll && PastHistoryTypeArr.indexOf(key) > -1) {
            newData[key] = oldVal || '';
            return false;
        }

        /**  SpecialKeyArr 都是数组类型 中会包含 toothNos，有的话就直接push，没有直接加到第一个元素上 */
        if (SpecialKeyArr.indexOf(key) > -1) {
            if (!Array.isArray(oldVal) || !Array.isArray(newVal)) {
                return false;
            }

            // 有 toothNos 直接拼接在老值后
            newData[key] = oldVal.concat(newVal.filter((it) => it.toothNos && it.toothNos.length));

            // 没有 toothNos 的填充在第一个老值后面
            const _arr = newVal.filter((it) => !it.toothNos || it.toothNos.length === 0).map((it) => it.value);

            if (!oldVal[0] || !oldVal[0].value) {
                return false;
            }
            // value 数组做合并（诊断），string 做拼接（辅查，处置等）
            if (Array.isArray(oldVal[0].value)) {
                oldVal[0].value = oldVal[0].value.concat(..._arr);
            } else if (typeof oldVal[0].value === 'string') {
                const _str = _arr.join('，');
                if (oldVal[0].value.indexOf(_str) === -1) {
                    oldVal[0].value += `<br>${_str || ''}`;
                }
            }
            newData[key] = oldVal;
            return false;
        }

        /** 新老值都是数组类型 直接合并 */
        if (Array.isArray(oldVal) && Array.isArray(newVal)) {
            newData[key] = oldVal.concat(newVal);
            return false;
        }

        /** 新老值都是对象类型 直接合并 */
        if (typeof oldVal === 'object' && typeof newVal === 'object') {
            newData[key] = Object.assign(newVal, oldVal);
            return false;
        }

        /** 新老值都是 json string，parse后是数组直接合并 */
        if (isJSON(oldVal) && isJSON(newVal)) {
            try {
                const oldArr = JSON.parse(oldVal);
                const newArr = JSON.parse(newVal);
                if (Array.isArray(oldArr) && Array.isArray(newArr)) {
                    newData[key] = JSON.stringify(oldArr.concat(newArr));
                }
            } catch (e) {
                console.warn(e);
            }
            return false;
        }

        /** 其余情况直接拼接字符串 */
        if (oldVal !== newVal) {
            newData[key] = `${oldVal || ''}<br>${newVal || ''}`;
        }
    });
}

export function toothNosHandler(toothNos, result) {
    if (toothNos?.length) {
        result.allToothNos.push(toothNos);
        const { maxQuadrantWidth } = getToothNosInfo(toothNos);
        if (maxQuadrantWidth > result.maxWidth) {
            result.maxWidth = maxQuadrantWidth;
        }
    }
}

export function getOutpatientToothNosInfo(medicalRecord, productForms, consultForms) {

    const result = {
        allToothNos: [],
        maxWidth: 42,
    };
    // 传了才处理
    if (medicalRecord) {
        const {
            dentistryExaminations = [],
            auxiliaryExaminations = [],
            extendDiagnosisInfos = [],
            treatmentPlans = [],
            disposals = [],
        } = medicalRecord;

        dentistryExaminations?.forEach((item) => {
            item && toothNosHandler(item.toothNos, result);
        });
        auxiliaryExaminations?.forEach((item) => {
            item && toothNosHandler(item.toothNos, result);
        });
        extendDiagnosisInfos?.forEach((item) => {
            item && toothNosHandler(item.toothNos, result);
        });
        treatmentPlans?.forEach((item) => {
            item && toothNosHandler(item.toothNos, result);
        });
        disposals?.forEach((item) => {
            item && toothNosHandler(item.toothNos, result);
        });
    }

    if (productForms) {
        productForms.forEach((form) => {
            form.productFormItems.forEach((item) => {
                toothNosHandler(item.toothNos, result);
            });
        });
    }

    if (consultForms) {
        consultForms.forEach((form) => {
            toothNosHandler(form.toothNos, result);
        });
    }

    return result;
}

export function getChargeToothNosInfo(chargeForms) {
    const result = {
        allToothNos: [],
        maxWidth: 42,
    };

    if (chargeForms) {
        chargeForms.forEach((form) => {
            form.chargeFormItems.forEach((item) => {
                toothNosHandler(item.toothNos, result);
            });
        });
    }

    return result;
}

/**
 * 咨询单的牙位处理
 */
export function getConsultToothNosInfo(medicalPlanForms) {
    const result = {
        allToothNos: [],
        maxWidth: 42,
    };

    if (medicalPlanForms) {
        medicalPlanForms.forEach((form) => {
            form.medicalPlanFormItems.forEach((item) => {
                toothNosHandler(item.toothNos, result);
            });
        });
    }

    return result;
}
