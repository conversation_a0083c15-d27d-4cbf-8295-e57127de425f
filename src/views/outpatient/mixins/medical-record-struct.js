import { mapGetters } from 'vuex';
import { MedicalRecordKeyLabelObj } from '@/common/constants/outpatient.js';
import SelectTemplateUseDialog from 'views/outpatient/medical-record-module/dialog/select-template-use-dialog/index.js';
import { isEqual } from 'utils/lodash.js';
import {
    appendMedicalRecordHandler,
    medicalRecordHasValue,
    PastHistoryTypeArr,
    getMedicalRecordStruct,
    AllMRKey,
    ClearKeyArr,
} from 'views/outpatient/common/medical-record/utils.js';
import SocialAPI from 'api/social.js';

export default {
    computed: {
        ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
        ...mapGetters('viewDistribute', ['viewDistributeConfig']),

        ...mapGetters('examination', ['isEnableCloudSupplier' ]),

        isEnableAiSearch() {
            return this.isEnableCloudSupplier;
        },
        /**
         * @desc 门诊处可填写字段受 门诊个人设置 影响
         * <AUTHOR>
         * @date 2020/05/11 10:23:15
         */
        switchSetting() {
            const config = this.outpatientEmployeeConfig;
            const {
                medicalRecord, diagnosisTreatment, prescription,
            } = config;
            return {
                /**
                 * @desc 如果是从病历模板过来，当前病历设置可能与病历模板字段不对齐，
                 * 此时处方单临时按病历模板的字段决定switch setting
                 * <AUTHOR>
                 * @date 2020/05/21 17:56:38
                 */
                medicalRecord: this.medicalRecordStruct ? this.medicalRecordStruct : medicalRecord,
                prescription,
                diagnosisTreatment,
            };
        },

        doctorAdviceInForm() {
            const { doctorAdviceInForm } = this.viewDistributeConfig.Outpatient;
            return doctorAdviceInForm;
        },
    },

    data() {
        return {
            medicalRecordStruct: null,
        };
    },

    methods: {
        /**
         * @desc 使用病历模板
         * <AUTHOR>
         * @date 2020/03/31 08:50:23
         */
        useMRTemplate(data, needMatchCode = true, isCoverAll = false) {
            // 使用模板过滤掉当前格式不存在的字段
            const _filterKey = [
                'chiefComplaint',
                'diagnosis',
                'extendDiagnosisInfos',
            ];
            for (const key in data) {
                if (
                    !_filterKey.includes(key) &&
                    !Object.prototype.hasOwnProperty.call(this.switchSetting.medicalRecord, key)
                ) {
                    data[key] = null;
                }
            }

            ClearKeyArr.forEach((key) => {
                data[key] = null;
            });

            const keyArr = Object.keys(this.switchSetting.medicalRecord);
            let _compareArr = [];
            if (isCoverAll) {
                _compareArr = keyArr;
            } else {
                // 既往史类别字段 调用模板不覆盖已有值
                PastHistoryTypeArr.forEach((key) => {
                    data[key] = this.postData.medicalRecord[key] || data[key] || '';
                });

                // 除既往史类别字段（PastHistoryTypeArr），模板和已有数据有冲突，需要提示插入还是覆盖
                _compareArr = keyArr.filter((it) => PastHistoryTypeArr.indexOf(it) === -1);
            }

            const diffNameArr = [];

            _compareArr.forEach((key) => {
                const oldVal = this.postData.medicalRecord[key];
                const newVal = data[key];

                if (!medicalRecordHasValue(key, newVal)) {
                    data[key] = oldVal;
                }
                if (medicalRecordHasValue(key, oldVal) && medicalRecordHasValue(key, newVal) && !isEqual(oldVal, newVal)) {
                    const name = MedicalRecordKeyLabelObj[key];
                    // 都有值但是不相等 需要做提示
                    if (name && diffNameArr.indexOf(name) === -1) {
                        diffNameArr.push(name);
                    }
                }

            });

            if (diffNameArr.length) {
                new SelectTemplateUseDialog({
                    diffNameArr,
                    onAppend: () => {
                        this.appendHandler(data, needMatchCode, isCoverAll);
                    },
                    onOverwrite: () => {
                        this.overwriteHandler(data, needMatchCode);
                    },
                }).generateDialog();
            } else {
                // 没有差异直接覆盖
                this.overwriteHandler(data, needMatchCode);
            }
        },

        appendHandler(data, needMatchCode = true, isCoverAll = false) {
            // 根据插入策略处理好 data 再做覆盖操作
            const keyArr = Object.keys(this.switchSetting.medicalRecord);
            if (!keyArr.includes('extendDiagnosisInfos') && keyArr.includes('diagnosis')) {
                keyArr.push('extendDiagnosisInfos');
            }
            appendMedicalRecordHandler(keyArr, this.postData.medicalRecord, data, isCoverAll);
            this.overwriteHandler(data, needMatchCode);
        },
        async overwriteHandler(result, needMatchCode = true) {
            AllMRKey.forEach((item) => {
                this.postData.medicalRecord[item.key] = result[item.key];
            });

            try {
                // 医嘱不在表单中，则代表在病历中（ex 口腔），需要从模版中复制
                if (!this.doctorAdviceInForm) {
                    this.postData.medicalRecord.doctorAdvice = result.doctorAdvice;
                }
            } catch (err) {
                console.error(err);
            }

            this.initMedicalRecordStruct();

            // 对于复制应用出来的病历模板f中的 诊断换取code，避免显示黄色提示
            if (!this.$abcSocialSecurity.isSupportMatchDiseaseCode) return false;
            const { medicalRecord } = this.postData || {};
            if (!medicalRecord?.extendDiagnosisInfos) return false;
            if (!needMatchCode) {
                await this.$nextTick();
                this.$abcEventBus.$emit('auto-search-diagnosis');
                return false;
            }
            for (const item of medicalRecord.extendDiagnosisInfos) {
                try {
                    const { data } = await SocialAPI.querySocialCodeByDiagnosis(item.value);
                    if (data && data.diagnosisInfos) {
                        item.value = data.diagnosisInfos;
                    }
                } catch (e) {
                    console.error(e);
                    break;
                }
            }


            if (this.isEnableAiSearch) {
                this.$abcEventBus.$emit('get-ai-examination-suggestion');
            }
        },

        initMedicalRecordStruct(useSetting = false) {
            const data = this.postData.medicalRecord;
            const config = this.outpatientEmployeeConfig;
            const { medicalRecord } = config;
            /**
             * @desc 当设置变动直接按新的设置
             * @desc 其他情况初始化，根据病历填写后的内容进行字段显隐
             * <AUTHOR>
             * @date 2020/05/22 15:55:34
             */
            if (useSetting) {
                this.medicalRecordStruct = Object.assign(
                    {},
                    medicalRecord,
                    {
                        chiefComplaint: 1,
                        diagnosis: 1,
                    },
                );
            } else {
                this.medicalRecordStruct = Object.assign(
                    {},
                    getMedicalRecordStruct(data, medicalRecord, this.doctorAdviceInForm),
                );
            }
        },
    },
};
