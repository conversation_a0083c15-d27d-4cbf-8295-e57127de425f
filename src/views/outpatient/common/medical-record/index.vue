<template>
    <div
        class="content-wrapper medical-record-wrapper"
        :class="{
            'is-disabled': disabled,
            'is-fixed': fixed,
        }"
    >
        <div v-if="onlyAgent && !expand" class="medical-record-tips">
            <div class="tips">
                门诊代录可省略主诉病史录入，需要录入时可开启
            </div>
            <template v-if="!disabledEdit">
                <abc-button
                    v-if="expand"
                    data-cy="mr-close-agent"
                    variant="text"
                    size="small"
                    @click="saveExpandState(false)"
                >
                    取消
                </abc-button>
                <abc-button
                    v-else
                    variant="text"
                    size="small"
                    data-cy="mr-open-agent"
                    @click="saveExpandState(true)"
                >
                    开启
                </abc-button>
            </template>
        </div>

        <medical-record-regis-info v-if="expand" :post-data="postData"></medical-record-regis-info>
        <template v-for="item in currentSortedMrStruct">
            <template v-if="expand">
                <div
                    v-if="item.key === 'chiefComplaint' && currentSwitchSetting.chiefComplaint"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">主诉</label>
                    <abc-form-item :required="!onlyAgent && !!item.required">
                        <div
                            style="display: flex; align-items: center;"
                        >
                            <component
                                :is="currentChiefComplaint"
                                v-model="chiefComplaint"
                                :fixed="fixed"
                                :disabled="disabledEdit"
                                style="flex: 1;"
                            ></component>

                            <!-- 发病日期 -->
                            <abc-date-picker
                                v-if="showSymptomTime"
                                v-model="symptomTime"
                                type="date"
                                :picker-options="{
                                    disabledDate(time) {
                                        return time.getTime() > Date.now();
                                    },
                                    shortcuts: [{
                                        text: '今天',
                                        onClick(cb) {
                                            const start = new Date();
                                            cb(start);
                                        }
                                    }, {
                                        text: '昨天',
                                        onClick(cb) {
                                            const start = new Date();
                                            start.setTime(start.getTime() - 24 * 60 * 60 * 1000);
                                            cb(start);
                                        }
                                    }],
                                }"
                                value-format="YYYY-MM-DD"
                                :disabled="disabledEdit"
                                style="padding-left: 8px;"
                            >
                                <div class="symptomTime-style">
                                    <span
                                        :class="{
                                            'symptomTime-title': !disabledEdit,
                                            'label-name': readonly && requireSymptomTime,
                                        }"
                                    >发病日期&nbsp;&nbsp;{{ symptomTime }}</span>
                                    <abc-icon v-if="!disabledEdit" icon="dropdown_triangle" class="symptomTime-icon"></abc-icon>
                                </div>
                            </abc-date-picker>
                        </div>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'birthHistory' && currentSwitchSetting.birthHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">出生史</label>
                    <abc-form-item :required="!!item.required">
                        <birth-history v-model="birthHistory" :fixed="fixed" :disabled="disabledEdit"></birth-history>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'presentHistory' && currentSwitchSetting.presentHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">现病史</label>
                    <abc-form-item :required="!!item.required">
                        <component
                            :is="currentPresentHistory"
                            v-model="presentHistory"
                            :fixed="fixed"
                            :chief-complaint="chiefComplaint"
                            spellcheck="false"
                            :disabled="disabledEdit"
                            :maxlength="3000"
                            label="现病史"
                            data-cy="abc-mr-现病史"
                        ></component>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'pastHistory' && currentSwitchSetting.pastHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">既往史</label>
                    <abc-form-item :required="!!item.required">
                        <past-history v-model="pastHistory" :fixed="fixed" :disabled="disabledEdit"></past-history>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'familyHistory' && currentSwitchSetting.familyHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">家族史</label>
                    <abc-form-item :required="!!item.required">
                        <family-history
                            v-model="familyHistory"
                            :is-childcare="isChildcare"
                            :fixed="fixed"
                            :disabled="disabledEdit"
                        ></family-history>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'allergicHistory' && currentSwitchSetting.allergicHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">过敏史</label>
                    <abc-form-item :required="!!item.required">
                        <allergy-history v-model="allergicHistory" :fixed="fixed" :disabled="disabledEdit"></allergy-history>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'personalHistory' && currentSwitchSetting.personalHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">个人史</label>
                    <abc-form-item :required="!!item.required">
                        <personal-history
                            v-model="personalHistory"
                            :fixed="fixed"
                            :disabled="disabledEdit"
                        ></personal-history>
                    </abc-form-item>
                </div>
                <!--病历设置开启情况下也只有女性患者才展示，月经婚育史-->
                <div
                    v-if="item.key === 'obstetricalHistory' && showObstetricalHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">月经婚育史</label>
                    <abc-form-item :required="!!item.required">
                        <obstetrical-history
                            v-model="obstetricalHistory"
                            :fixed="fixed"
                            :disabled="disabledEdit"
                        ></obstetrical-history>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'epidemiologicalHistory' && currentSwitchSetting.epidemiologicalHistory"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">流行病史</label>
                    <abc-form-item :required="!!item.required">
                        <epidemiological-history
                            v-model="epidemiologicalHistory"
                            :fixed="fixed"
                            :warn-days="$abcSocialSecurity.config.isZhejiangHangzhou ? 28 : 14"
                            :disabled="disabledEdit"
                        ></epidemiological-history>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'physicalExamination' && currentSwitchSetting.physicalExamination"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">体格检查</label>
                    <abc-form-item :required="!!item.required">
                        <physical-examination
                            key="physical-examination"
                            v-model="physicalExamination"
                            :is-childcare="isChildcare"
                            :medical-record-type="mr.type"
                            :fixed="fixed"
                            :type="0"
                            :disabled="disabledEdit"
                            label="体格检查"
                        ></physical-examination>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'chineseExamination' && currentSwitchSetting.chineseExamination"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">望闻切诊</label>
                    <abc-form-item :required="!!item.required">
                        <chinese-examination
                            key="chinese-examination"
                            v-model="chineseExamination"
                            :fixed="fixed"
                            :disabled="disabledEdit"
                            :patient-info="patientInfo"
                            :outpatient-sheet-id="postData.id"
                            :is-open-source="isOpenSource"
                            :attachments.sync="attachments"
                        >
                        </chinese-examination>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'oralExamination' && currentSwitchSetting.oralExamination"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">口腔检查</label>
                    <abc-form-item :required="!!item.required">
                        <oral-examination v-model="oralExamination" :fixed="fixed" :disabled="disabledEdit"></oral-examination>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'auxiliaryExaminations' && currentSwitchSetting.auxiliaryExaminations"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">辅助检查</label>
                    <abc-form-item :required="!!item.required">
                        <auxiliary-examinations
                            v-model="auxiliaryExaminations"
                            :fixed="fixed"
                            :disabled="disabledEdit"
                        ></auxiliary-examinations>
                    </abc-form-item>
                </div>
                <div
                    v-if="item.key === 'syndromeTreatment' && currentSwitchSetting.syndromeTreatment"
                    :key="item.key"
                    class="medical-record-item"
                >
                    <label :class="{ 'label-name': readonly && item.required }">辨证论治</label>
                    <abc-form-item :required="!!item.required">
                        <abc-edit-div
                            v-model="syndromeTreatment"
                            :fixed="fixed"
                            spellcheck="false"
                            :disabled="disabledEdit"
                            data-cy="abc-mr-辨证论治"
                        ></abc-edit-div>
                    </abc-form-item>
                </div>
            </template>

            <div
                v-if="item.key === 'diagnosis' &&
                    (currentSwitchSetting.diagnosis || currentSwitchSetting.syndrome)"
                :key="item.key"
                class="medical-record-item"
            >
                <template v-if="currentSwitchSetting.diagnosis">
                    <label :class="{ 'label-name': readonly && item.required }">{{ diagnosisLabel }}</label>
                    <abc-form-item :required="!!item.required" class="tcm-syndrome">
                        <div style="display: flex; align-items: center;">
                            <extend-diagnosis-infos
                                :key="`${mr.type }tcm-disease`"
                                v-model="extendDiagnosisInfos"
                                :diagnosis.sync="diagnosis"
                                :post-data="postData"
                                :medical-record-type="medicalRecordType"
                                :fixed="fixed"
                                :disabled="disabledEdit || disabledDiagnosis"
                                :must-match-disease-code="mustMatchDiseaseCode"
                                :must-at-least-one-western-disease="mustAtLeastOneWesternDisease"
                                :diagnosis-search-type="diagnosisSearchType"
                                style="flex: 1;"
                            >
                            </extend-diagnosis-infos>
                            <!-- 传染病警告 -->
                            <div
                                v-if="isOnInfectiousDiseaseWarn && isShowInfectedDiseaseWarn"
                                style="cursor: pointer;"
                                @click="isShowInfectedDiseaseWarnDialog = true"
                            >
                                <infectious-disease-warn></infectious-disease-warn>
                            </div>
                        </div>
                    </abc-form-item>
                </template>
                <template v-if="currentSwitchSetting.syndrome">
                    <label
                        :class="{
                            'label-name': readonly && requireSyndrome,
                            'second-label': !!currentSwitchSetting.diagnosis,
                        }"
                    >辨证</label>
                    <abc-form-item class="tcm-syndrome" :required="requireSyndrome">
                        <extend-diagnosis-infos
                            v-if="useSyndromeV2"
                            key="tcm-syndrome"
                            v-model="syndromes"
                            :diagnosis-search-type="DiagnosisSearchTypeEnum.SYNDROME"
                            :post-data="postData"
                            :medical-record-type="medicalRecordType"
                            fixed
                            :disabled="disabledEdit"
                            :must-match-disease-code="mustMatchDiseaseCode"
                            style="flex: 1;"
                        >
                        </extend-diagnosis-infos>
                        <diagnosis
                            v-else
                            key="tcm-syndrome"
                            v-model="syndrome"
                            :medical-record-type="mr.type"
                            :diagnosis="diagnosis"
                            :fixed="fixed"
                            :disabled="disabledEdit"
                            :maxlength="1000"
                            client="tcm-syndrome"
                            label="辨证"
                        >
                        </diagnosis>
                    </abc-form-item>
                </template>
            </div>
            <div
                v-if="item.key === 'therapy' &&
                    (currentSwitchSetting.therapy || currentSwitchSetting.chinesePrescription)"
                :key="item.key"
                class="medical-record-item"
            >
                <template v-if="currentSwitchSetting.therapy">
                    <label :class="{ 'label-name': readonly && item.required }">治法</label>
                    <abc-form-item :required="!!item.required">
                        <diagnosis
                            key="tcm-therapy"
                            v-model="therapy"
                            :fixed="fixed"
                            :medical-record-type="mr.type"
                            :disabled="disabledEdit"
                            client="tcm-therapy"
                            label="治法"
                        >
                        </diagnosis>
                    </abc-form-item>
                </template>

                <template v-if="currentSwitchSetting.chinesePrescription">
                    <label
                        :class="{
                            'label-name': readonly && requireChinesePrescription,
                            'second-label': !!currentSwitchSetting.therapy,
                        }"
                    >方药</label>
                    <abc-form-item :required="requireChinesePrescription">
                        <diagnosis
                            key="tcm-prescription"
                            v-model="chinesePrescription"
                            :fixed="fixed"
                            :medical-record-type="mr.type"
                            :disabled="disabledEdit"
                            client="tcm-prescription"
                            label="方药"
                            @changeDisease="changeTCMPRHandle"
                        >
                        </diagnosis>
                    </abc-form-item>
                </template>
            </div>
            <div
                v-if="item.key === 'disposals' && currentSwitchSetting.disposals"
                :key="item.key"
                class="medical-record-item"
            >
                <label :class="{ 'label-name': readonly && item.required }">处置</label>
                <abc-form-item :required="!!item.required">
                    <auxiliary-examinations
                        v-model="disposals"
                        :disabled="disabledEdit"
                    ></auxiliary-examinations>
                </abc-form-item>
            </div>
            <div
                v-if="item.key === 'target' &&
                    (currentSwitchSetting.prognosis || currentSwitchSetting.target)"
                :key="item.key"
                class="medical-record-item"
            >
                <template v-if="currentSwitchSetting.target">
                    <label :class="{ 'label-name': readonly && item.required }">目标</label>
                    <abc-form-item :required="!!item.required">
                        <abc-edit-div
                            v-model="target"
                            :fixed="fixed"
                            spellcheck="false"
                            :disabled="disabledEdit"
                            :maxlength="1000"
                        ></abc-edit-div>
                    </abc-form-item>
                </template>

                <template v-if="currentSwitchSetting.prognosis">
                    <label
                        :class="{
                            'label-name': readonly && requirePrognosis,
                            'second-label': !!currentSwitchSetting.target,
                        }"
                    >预后</label>
                    <abc-form-item :required="requirePrognosis">
                        <abc-edit-div
                            v-model="prognosis"
                            :fixed="fixed"
                            spellcheck="false"
                            :disabled="disabledEdit"
                            :maxlength="1000"
                        ></abc-edit-div>
                    </abc-form-item>
                </template>
            </div>
            <div
                v-if="item.key === 'doctorAdvice' && currentSwitchSetting.doctorAdvice && !doctorAdviceInForm"
                :key="item.key"
                class="medical-record-item"
            >
                <label :class="{ 'label-name': readonly && item.required }">医嘱建议</label>
                <abc-form-item :required="!!item.required">
                    <doctor-advice
                        v-model="mr.doctorAdvice"
                        :disabled="disabledEdit"
                        :fixed="fixed"
                        :department-id="postData.departmentId"
                        :department-name="postData.departmentName"
                        :diagnosis="mr.diagnosis"
                    ></doctor-advice>
                </abc-form-item>
            </div>
        </template>
        <div v-if="currentSwitchSetting.preDiagnosisAttachments" class="medical-record-item">
            <label>附件</label>
            <div class="abc-form-item">
                <external-file-common
                    v-model="preDiagnosisAttachments"
                    :business-type="BusinessTypeEnum.REGISTRATION_MEDICAL_IMAGE"
                    oss-filepath="registration"
                    :max-upload-count="20"
                    :disabled="disabledEdit"
                    :patient-info="patient"
                    :business-id="preDiagnosisKeyId"
                    business-desc="挂号预诊"
                    upload-description="附件支持图片、PDF格式"
                ></external-file-common>
            </div>
        </div>

        <div v-show="showExternalFile" class="medical-record-item">
            <label>附件</label>
            <div class="abc-form-item">
                <external-file
                    ref="eReport"
                    v-model="attachments"
                    :outpatient-sheet-id="postData.id"
                    :outpatient-created="postData.created"
                    :patient-id="patientInfo.id"
                    :question-sheets.sync="postData.questionSheets"
                    :disabled="disabledEdit"
                    :patient-info="patientInfo"
                    :disabled-update-question-form="disabledUpdateQuestionForm"
                    @delete-questionForm="$emit('delete-questionForm')"
                ></external-file>
            </div>
        </div>

        <div v-show="medicalDocumentList.length" class="medical-record-item">
            <label>文书</label>
            <div class="abc-form-item">
                <medical-document-file :list="medicalDocumentList" @medical-document-click="handleMedicalDocumentClick"></medical-document-file>
            </div>
        </div>

        <slot></slot>

        <!-- 诊断传染病列表弹窗 -->
        <infectious-disease-list-dialog
            v-if="isShowInfectedDiseaseWarnDialog"
            v-model="isShowInfectedDiseaseWarnDialog"
            :infected-disease-diagnosis="infectedDiseaseDiagnosis"
        ></infectious-disease-list-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import { mapGetters } from 'vuex';

    import ChiefComplaint from './chief-complaint.vue';
    import ChineseChiefComplaint from './chinese-chief-complaint.vue';
    import DentistryChiefComplaint from '@/views-dentistry/outpatient/common/medical-record/chief-complaint.vue';
    import ChinesePresentHistory from './chinese-present-history.vue';
    import PresentHistory from './present-history.vue';
    import { EditDiv } from '@abc/ui-pc';
    import PastHistory from './past-history.vue';
    import BirthHistory from './birth-history.vue';
    import FamilyHistory from './family-history.vue';
    import PersonalHistory from './personal-history.vue';
    import ObstetricalHistory from './obstetrical-history.vue';
    import EpidemiologicalHistory from './epidemiological-history';
    import PhysicalExamination from './physical-examination.vue';
    import ChineseExamination from './chinese-examination.vue';
    import OralExamination from './oral-examination.vue';
    import AuxiliaryExaminations from './auxiliary-examinations.vue';
    import Diagnosis from './diagnosis.vue';
    import ExtendDiagnosisInfos from './extend-diagnosis-infos.vue';
    import DoctorAdvice from 'src/views/outpatient/common/medical-record/doctor-advise.vue';
    import InfectiousDiseaseWarn from './infectious-disease-warn.vue';
    import InfectiousDiseaseListDialog from './infectious-disease-list-dialog.vue';
    import ExternalFile from 'src/views/outpatient/common/medical-record/external-file';
    import { BusinessTypeEnum } from '@abc/constants';
    import { DiagnosisSearchTypeEnum } from '@/common/constants/shebao.js';
    import infectiousDiseaseFilter from 'src/views/outpatient/common/medical-record/infectiousDiseaseFilter';
    import MedicalDocumentFile from 'views/outpatient/common/medical-record/medical-document-file.vue';
    import MedicalRecordRegisInfo from 'src/views/outpatient/common/medical-record/medical-record-regis-info.vue';

    import { loosePlainText } from 'utils/xss-filter.js';
    import AbcSocket from 'views/common/single-socket.js';
    import ExternalFileCommon from 'views/layout/external-file/index.vue';
    import AllergyHistory from 'views/outpatient/common/medical-record/allergy-history.vue';
    import { formatDate } from '@abc/utils-date';
    import {
        createGUID, isNumber,
    } from '@/utils';
    import {
        getMrTypePropertyKey,
        MedicalRecordTypeEnum,
    } from 'views/outpatient/common/medical-record/utils';
    import TongueAppearance from 'views/outpatient/common/medical-record/tongue-appearance.vue';
    import PulseDiagnosis from 'views/outpatient/common/medical-record/pulse-diagnosis.vue';
    export default {

        components: {
            PulseDiagnosis,
            TongueAppearance,
            AllergyHistory,
            ChiefComplaint,
            PastHistory,
            BirthHistory,
            FamilyHistory,
            PersonalHistory,
            ObstetricalHistory,
            EpidemiologicalHistory,
            PhysicalExamination,
            ChineseExamination,
            OralExamination,
            AuxiliaryExaminations,
            Diagnosis,
            ExtendDiagnosisInfos,
            DoctorAdvice,
            ExternalFile,
            ExternalFileCommon,
            InfectiousDiseaseWarn,
            InfectiousDiseaseListDialog,
            MedicalDocumentFile,
            MedicalRecordRegisInfo,
        },

        mixins: [infectiousDiseaseFilter],

        props: {
            postData: {
                type: Object,
                default: () => {
                    return { medicalRecord: { extendDiagnosisInfos: [] } };
                },
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            disabled: Boolean,
            readonly: Boolean,
            onlyAgent: Boolean, // 代录相关
            value: Object,
            status: Number,
            fixed: {
                type: Boolean,
                default: false,
            },
            disabledDiagnosis: {
                type: Boolean,
                default: false,
            },
            switchSetting: {
                type: Object,
                default: () => {
                    return {
                        chiefComplaint: 0, // 主诉
                        presentHistory: 0, // 现病史
                        pastHistory: 0, // 既往史
                        personalHistory: 0, // 个人史
                        obstetricalHistory: 0, // 月经婚育史
                        epidemiologicalHistory: 0, // 流行病史
                        physicalExamination: 0, // 体格检查
                        chineseExamination: 0, // 望闻问切
                        oralExamination: 0, // 口腔检查
                        auxiliaryExaminations: 0, // 辅助检查
                        diagnosis: 0, // 诊断
                        syndrome: 0, // 辩证
                        therapy: 0, // 治法
                        chinesePrescription: 0, // 方药
                        disposals: 0, // 处置
                        preDiagnosisAttachments: 0, // 上传附件
                        // 默认关闭，儿保新加打开
                        familyHistory: 0, //家庭史
                        birthHistory: 0, // 出生史，儿保特有
                        prognosis: 0, // 预后
                        target: 0, // 目标
                        allergicHistory: 0, // 过敏史
                        symptomTime: 0, // 发病日期
                    };
                },
            },

            isChildcare: {
                type: Boolean,
                default: false,
            },
            fromModule: {
                type: String,
                default: 'outpatient',
            },
            disabledUpdateQuestionForm: {
                type: Boolean,
                default: false,
            },

            mustMatchDiseaseCode: {
                type: Boolean,
                default: false,
            },

            mustAtLeastOneWesternDisease: {
                type: Boolean,
                default: false,
            },

            medicalDocumentList: {
                type: Array,
                default: () => [],
            },
            diagnosisLabel: {
                type: String,
                default: '诊断',
            },
            sortedMrStruct: Array,
            isOpenSource: Boolean,
        },

        data() {
            return {
                expand: true,
                DiagnosisSearchTypeEnum,
                BusinessTypeEnum,
                isShowInfectedDiseaseWarnDialog: false, // 是否显示传染病诊断弹窗
                diagnosisList: [], // 诊断列表
                isShowInfectedDiseaseWarn: false, // 是否显示传染病警告,通过判断诊断结果得来
                isOnInfectiousDiseaseWarn: false, // 是否开启传染病监测,在设置中是否开启
                infectedDiseaseDiagnosis: {
                    classA: [], classB: [], classC: [],
                }, // 传染病诊断结果
                preDiagnosisKeyId: createGUID(),
            };
        },
        computed: {
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig', 'clinicMedicalRecordConfig']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            ...mapGetters([
                'userInfo',
                'currentClinic',
            ]),
            // 新版辨证，结构和诊断一致
            useSyndromeV2() {
                return this.$abcSocialSecurity.isOpenSocial &&
                    this.$abcSocialSecurity.config.isLiaoningDalian;
            },
            diagnosisSearchType() {
                if (this.useSyndromeV2) {
                    return [
                        DiagnosisSearchTypeEnum.WESTERN,
                        DiagnosisSearchTypeEnum.CHINESE,
                        DiagnosisSearchTypeEnum.THERAPY,
                    ].join(',');
                }
                return undefined;
            },

            disabledEdit() {
                return this.disabled || this.readonly;
            },

            needRequired() {
                return this.fromModule === 'outpatient' || this.readonly;
            },

            currentSortedMrStruct() {
                if (this.sortedMrStruct) {
                    return this.sortedMrStruct.map((x) => ({
                        ...x,
                        required: this.needRequired && x.required,
                    }));
                }
                return this.clinicMedicalRecordConfig[getMrTypePropertyKey()[this.mr.type ?? this.medicalRecordType]].map((x) => ({
                    ...x,
                    required: this.needRequired && x.required,
                }));
            },
            medicalRecordType() {
                if (isNumber(this.mr.type)) return this.mr.type;
                const config = this.outpatientEmployeeConfig;
                if (!config) return 0;
                if (!config.medicalRecord) return 0;
                return config.medicalRecord.type || 0;
            },

            currentChiefComplaint() {
                if (this.medicalRecordType === MedicalRecordTypeEnum.CHINESE) {
                    return ChineseChiefComplaint;
                }
                if (this.medicalRecordType === MedicalRecordTypeEnum.ORAL) {
                    return DentistryChiefComplaint;
                }
                return ChiefComplaint;
            },

            currentPresentHistory() {
                if (this.medicalRecordType === MedicalRecordTypeEnum.CHINESE) {
                    return ChinesePresentHistory;
                }
                if (this.medicalRecordType === MedicalRecordTypeEnum.ORAL) {
                    return EditDiv;
                }
                return PresentHistory;
            },

            doctorAdviceInForm() {
                return this.viewDistributeConfig.Outpatient.doctorAdviceInForm;
            },

            patientInfo() {
                return this.postData?.patient || {};
            },
            showSymptomTime() {
                return this.currentSwitchSetting.symptomTime &&
                    (!(this.disabledEdit && !this.mr.symptomTime) || this.fromModule === 'template');
            },
            showObstetricalHistory() {
                return this.currentSwitchSetting.obstetricalHistory &&
                    (this.fromModule === 'template' || this.patientInfo.sex === '女');
            },
            showExternalFile() {
                if (!this.postData) return false;
                return (
                    this.showEReport ||
                    this.postData?.medicalRecord?.attachments?.length ||
                    this.postData?.questionSheets?.length
                );
            },

            currentSwitchSetting() {
                return Object.assign({}, this.switchSetting || {});
            },

            mr: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            chiefComplaint: {
                get() {
                    return this.mr.chiefComplaint || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.chiefComplaint = val;
                },
            },

            symptomTime: {
                get() {
                    const date = this.mr.symptomTime || this.postData.created;
                    return formatDate(date);
                },
                set(val) {
                    // 使用 Date 构造函数来创建 Date 对象
                    let dateObject = new Date(val);
                    // 确保 dateObject 是有效的 Date 对象
                    if (isNaN(dateObject.getTime())) {
                        dateObject = new Date();
                    }
                    this.mr.symptomTime = dateObject.toISOString();
                },
            },

            birthHistory: {
                get() {
                    return this.mr.birthHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.$set(this.mr, 'birthHistory', val);
                },
            },

            pastHistory: {
                get() {
                    return this.mr.pastHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.pastHistory = val;
                },
            },

            allergicHistory: {
                get() {
                    return this.mr.allergicHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.allergicHistory = val;
                },
            },

            familyHistory: {
                get() {
                    return this.mr.familyHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.familyHistory = val;
                },
            },
            preDiagnosisAttachments: {
                get() {
                    return this.mr.preDiagnosisAttachments || [];
                },
                set(val) {
                    this.mr.preDiagnosisAttachments = val;
                },
            },

            personalHistory: {
                get() {
                    return this.mr.personalHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.personalHistory = val;
                },
            },

            obstetricalHistory: {
                get() {
                    return this.mr.obstetricalHistory || '';
                },
                set(val) {
                    val = loosePlainText(val, false, { span: ['class'] });
                    this.mr.obstetricalHistory = val;
                },
            },

            epidemiologicalHistory: {
                get() {
                    return this.mr.epidemiologicalHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.epidemiologicalHistory = val;
                },
            },

            physicalExamination: {
                get() {
                    return this.mr.physicalExamination || '';
                },
                set(val) {
                    val = loosePlainText(val, false, {
                        a: ['class', 'data-tipsy'],
                        i: ['class', 'contenteditable'],
                    });
                    this.mr.physicalExamination = val;
                },
            },

            chineseExamination: {
                get() {
                    return this.mr.chineseExamination || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.chineseExamination = val;
                },
            },

            tongue: {
                get() {
                    return this.mr.tongue || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.$set(this.mr, 'tongue', val);
                },
            },

            pulse: {
                get() {
                    return this.mr.pulse || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.$set(this.mr, 'pulse', val);
                },
            },

            oralExamination: {
                get() {
                    return this.mr.oralExamination || '';
                },
                set(val) {
                    val = loosePlainText(val, false, { span: ['class'] });
                    this.mr.oralExamination = val;
                },
            },
            auxiliaryExaminations: {
                get() {
                    return this.mr.auxiliaryExaminations || [];
                },
                set(val) {
                    val.forEach((item) => {
                        item.value = loosePlainText(item.value);
                    });
                    this.mr.auxiliaryExaminations = val;
                },
            },

            syndromeTreatment: {
                get() {
                    return this.mr.syndromeTreatment || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.syndromeTreatment = val;
                },
            },

            extendDiagnosisInfos: {
                get() {
                    return this.mr.extendDiagnosisInfos || [];
                },
                set(val) {
                    this.mr.extendDiagnosisInfos = val;
                },
            },

            diagnosis: {
                get() {
                    return this.mr.diagnosis || '';
                },
                set(val) {
                    val = loosePlainText(val, false, {
                        a: ['class', 'data-tipsy'],
                        i: ['class', 'contenteditable'],
                    });
                    this.mr.diagnosis = val;
                },
            },

            /**
             * @desc 中医辩证
             * <AUTHOR>
             * @date 2020/05/08 10:24:54
             */
            syndrome: {
                get() {
                    return this.mr.syndrome || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.syndrome = val;
                },
            },

            // 中医辨证v2
            syndromes: {
                get() {
                    return this.mr.syndromes || [];
                },
                set(val) {
                    this.mr.syndromes = val;
                },
            },

            presentHistory: {
                get() {
                    return this.mr.presentHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.presentHistory = val;
                },
            },

            /**
             * @desc 中医治法
             * <AUTHOR>
             * @date 2020/05/11 09:06:08
             */
            therapy: {
                get() {
                    return this.mr.therapy || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.therapy = val;
                },
            },

            /**
             * @desc 中医方药
             * <AUTHOR>
             * @date 2020/05/11 09:06:08
             */
            chinesePrescription: {
                get() {
                    return this.mr.chinesePrescription || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.chinesePrescription = val;
                },
            },
            attachments: {
                get() {
                    return this.mr.attachments || [];
                },
                set(val) {
                    this.mr.attachments = val;
                },
            },
            prognosis: {
                get() {
                    return this.mr.prognosis || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.prognosis = val;
                },
            },
            target: {
                get() {
                    return this.mr.target || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.target = val;
                },
            },
            // 处置
            disposals: {
                get() {
                    return this.mr.disposals || '';
                },
                set(val) {
                    val.forEach((item) => {
                        item.value = loosePlainText(item.value);
                    });
                    this.mr.disposals = val;
                },
            },
            requireSymptomTime() {
                const target = this.currentSortedMrStruct.find((x) => x.key === 'symptomTime');
                return target && !!target.required;
            },
            requireSyndrome() {
                const target = this.currentSortedMrStruct.find((x) => x.key === 'syndrome');
                return target && !!target.required;
            },
            requireChinesePrescription() {
                const target = this.currentSortedMrStruct.find((x) => x.key === 'chinesePrescription');
                return target && !!target.required;
            },
            requirePrognosis() {
                const target = this.currentSortedMrStruct.find((x) => x.key === 'prognosis');
                return target && !!target.required;
            },
        },

        watch: {
            status: {
                handler(val) {
                    if (val > 0) {
                        this.expand = true;
                    } else {
                        this.expand = this.loadExpandState();
                    }
                },
                immediate: true,
            },
            // 监听诊断结果
            'postData.medicalRecord.extendDiagnosisInfos': {
                handler(val) {
                    this.handleDiagnosisList(val);
                },
                immediate: true,
                deep: true,
            },
            // 监听诊断结果,筛选出传染病
            diagnosisList: {
                handler(val) {
                    this.handleInfectiousDiseaseList(val);
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleMobileUploadImages);
        },
        beforeDestroy() {
            this._socket.off('short-url.upload_attachment', this.handleMobileUploadImages);
        },

        methods: {
            changeTCMPRHandle(val) {
                this.$emit('add-prescription', val);
            },
            handleMobileUploadImages(data) {
                const {
                    attachments = [],
                    businessType,
                    businessId,
                } = data;
                if (
                    businessType === BusinessTypeEnum.REGISTRATION_MEDICAL_IMAGE &&
                    businessId === this.preDiagnosisKeyId &&
                    attachments?.length
                ) {
                    attachments.forEach((item) => {
                        if (Array.isArray(this.mr.preDiagnosisAttachments)) {
                            const isExist = this.mr.preDiagnosisAttachments.find((it) => it.id === item.id);
                            if (!isExist) {
                                this.mr.preDiagnosisAttachments.push(item);
                            }
                        }
                    });
                }
            },
            handleMedicalDocumentClick(...props) {
                this.$emit('medical-document-click', ...props);
            },
            // 保存 expand 状态到 localStorage
            saveExpandState(val) {
                this.expand = val;
                if (this.currentClinic && this.userInfo) {
                    const key = `medical_record_expand_${this.currentClinic.id}_${this.userInfo.id}`;
                    localStorage.setItem(key, this.expand);
                }
            },
            // 从 localStorage 中读取 expand 状态
            loadExpandState() {
                if (!this.currentClinic || !this.userInfo) {
                    return !this.onlyAgent;
                }
                if (this.currentClinic && this.userInfo) {
                    const key = `medical_record_expand_${this.currentClinic.id}_${this.userInfo.id}`;
                    const savedState = localStorage.getItem(key);
                    if (savedState !== null) {
                        return savedState === 'true';
                    }
                    return !this.onlyAgent;

                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common';

.symptomTime-style {
    display: flex;
    align-items: center;
    padding-right: 8.5px !important;
    font-size: 13px;
    font-weight: 400;
    line-height: 16px;
    color: $T2;
    cursor: pointer;

    .symptomTime-icon {
        margin-left: 5px;
        color: $P1;
    }

    &:hover {
        .symptomTime-title {
            color: #000000;
        }

        .symptomTime-icon {
            color: $T2 !important;
        }
    }
}
</style>
