<template>
    <div class="extend-diagnosis-infos-wrapper">
        <div v-for="(item, index) in currentValue" :key="index" class="medical-record-item-group">
            <diagnosis-infos
                v-model="item.value"
                :disabled="disabled"
                :fixed="fixed"
                :post-data="postData"
                :medical-record-type="medicalRecordType"
                :need-aide-suggestions="needAideSuggestions"
                :must-match-disease-code="mustMatchDiseaseCode"
                :must-at-least-one-western-disease="mustAtLeastOneWesternDisease"
                :diagnosis-search-type="diagnosisSearchType"
            ></diagnosis-infos>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import DiagnosisInfos from '@/views/outpatient/common/medical-record/diagnosis-info.vue';
    import common from 'components/common/form.js';
    import { keepLastIndex } from 'utils/dom.js';

    export default {
        name: 'DentistryDiagnosis',
        components: {
            DiagnosisInfos,
        },
        mixins: [common],
        props: {
            value: {
                type: Array,
                required: true,
            },
            postData: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            disabled: Boolean,
            fixed: {
                type: Boolean,
                default: false,
            },
            diagnosis: {
                type: String,
                default: '',
            },
            fetchSuggestions: Function, // 获取is-focus展开项

            // 是否需要智能推荐
            needAideSuggestions: {
                type: Boolean,
                default: true,
            },
            // 是否需要匹配疾病编码
            mustMatchDiseaseCode: {
                type: Boolean,
                default: false,
            },
            medicalRecordType: {
                type: Number,
                default: 0,
            },
            // 至少包含一个西医诊断
            mustAtLeastOneWesternDisease: {
                type: Boolean,
                default: false,
            },
            diagnosisSearchType: [String, Number],
        },
        data() {
            return {
                currentRefIndex: 0,
                showSuggestions: false,
                suggestionsStyle: {},
                currentValue: [{ value: [] }],
            };
        },

        watch: {
            value: {
                handler (val) {
                    if (val && val.length > 0) {
                        this.currentValue = val;
                    } else {
                        // 没有值的情况需要默认值
                        this.$emit('input', [{ value: [] }]);
                    }
                    this.updateStyle();
                },
                deep: true,
                immediate: true,
            },
        },
        beforeDestroy() {
            this._focuseTimer && clearTimeout(this._focuseTimer);
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            updateStyle() {
                this._timer = setTimeout(() => {
                    const $refTargets = this.$refs['ref-target'];
                    if (!$refTargets) return;
                    let top = 0;
                    $refTargets.forEach((target,index) => {
                        if (index <= this.currentRefIndex) {
                            top += target.$el.offsetHeight;
                        }
                    });
                    top += 4;
                    this.suggestionsStyle = {
                        top: this.fixed ? '0' : `${top}px`,
                    };
                }, 50);
            },
            focusInput() {
                this._focuseTimer = setTimeout(() => {
                    const $refTargets = this.$refs['ref-target'];
                    if (!$refTargets) return;
                    const { $el } = $refTargets[this.currentRefIndex];
                    $el.focus();
                    keepLastIndex($el);
                }, 250);
            },
            deleteGroup(index) {
                this.currentValue.splice(index,1);
            },
            handleClick(index) {
                if (this.disabled) return false;
                this.currentRefIndex = index;
                this.showSuggestions = true;
                this.updateStyle();
            },
            handleTab() {
                this.outside();
            },
            handleDown() {
                this.outside();
            },
            handleUp() {
                this.outside();
            },
            outside() {
                this.showSuggestions = false;
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
    .extend-diagnosis-infos-wrapper {
        .aide-diagnosis-wrapper {
            flex: 1;
        }

        .diagnosis-social-info-wrapper {
            display: flex;
        }
    }
</style>


