<template>
    <div v-abc-click-outside="outside" class="aide-diagnosis-wrapper">
        <div class="diagnosis-social-info-wrapper">
            <abc-edit-div
                ref="ref-target"
                :key="forceUpdateFlag"
                v-model="currentValue"
                :class="{ 'is-focus': showSuggestions }"
                :disabled="disabled"
                :xss-options="{
                    a: ['class', 'data-tipsy'],
                    i: ['class', 'contenteditable']
                }"
                :data-cy="`abc-mr-${label}`"
                :maxlength="maxlength"
                @click="handleClick"
                @focus="handleFocus"
                @blur="handleBlur"
                @input="handleInput"
                @tab="handleTab"
                @down="handleDown"
                @up="handleUp"
                @enter="enterEvent"
            >
            </abc-edit-div>
        </div>

        <input
            ref="abcinput"
            type="text"
            style=" position: absolute; top: 0; left: 0; width: 0; opacity: 0;"
            tabindex="-1"
            :value="value"
        />

        <div
            v-if="showSuggestions && suggestions.length"
            ref="popper-target"
            :class="{
                fixed: fixed, 'is-diagnosis': !client
            }"
            class="medical-record-suggestions-wrapper search-results suggestions-wrapper"
            :style="suggestionsStyle"
        >
            <dl
                v-for="(s, index) in suggestions"
                :key="s.id"
                class="suggestions-item"
                :class="{ selected: index === currentIndex }"
                :data-cy="`abc-mr-${label}-${s.name}`"
                @mousedown.stop.prevent="selectAideDisease(s)"
            >
                <dt class="diagnosis-name">
                    {{ s.name }}
                </dt>
            </dl>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import CDSSAPI from 'api/cdss/index';
    import SocialAPI from 'api/social.js';
    import OutpatientAPI from 'api/outpatient.js';

    // utils
    import { debounce } from 'utils/lodash';
    import { mapGetters } from 'vuex';
    import common from 'components/common/form';
    import popper from './popper';
    import diagnosisSocialCodeHandle from 'src/views/common/diagnosis-social-code-handle';
    import { HangzhouDiseaseTypeLabel } from 'src/views/common/social-info.js';
    import { isJSON } from '@/utils';
    import { keepLastIndex } from '@/utils/dom.js';

    export default {
        name: 'Diagnosis',

        components: {
        },

        mixins: [common, popper, diagnosisSocialCodeHandle],
        props: {
            value: String,
            diagnosis: String,
            fetchSuggestions: Function, // 获取is-focus展开项
            placeholder: String,
            client: String, // 用于cdss搜索的client
            disabled: Boolean,
            medicalRecordType: Number,

            // 是否需要智能推荐
            needAideSuggestions: {
                type: Boolean,
                default: true,
            },
            medicalRecord: {
                type: Object,
            },
            label: String,
            maxlength: Number,
        },
        data() {
            return {
                HangzhouDiseaseTypeLabel,
                currentValue: '',
                currentList: [],
                showSuggestions: false,
                suggestions: [],
                asyncValue: '',
                loading: false,
                suggestionsStyle: {},

                currentIndex: -1,
                isFocusing: false,

                forceUpdateFlag: 0,
            };
        },

        computed: {
            ...mapGetters('aiDiagnosis', [
                'diseases',
                'sickness',
            ]),
            aides() {
                if (this.medicalRecordType === 1) {
                    const list = [];
                    // 辨证
                    if (this.client === 'tcm-syndrome') {
                        this.sickness.forEach((sick) => {
                            // 如果有诊断，需要返回该诊断下的辨证
                            if (!this.diagnosis || this.diagnosis.indexOf(sick.diseaseName) > -1) {
                                sick.concomitant_symptom.forEach((item) => {
                                    if (!list.find((it) => it.name === item.symptomName)) {
                                        list.push({
                                            id: item.symptomName,
                                            name: item.symptomName,
                                        });
                                    }
                                });
                            }
                        });
                        // 如果该诊断下没有 辨证推荐，则返回所有
                        if (list.length === 0) {
                            this.sickness.forEach((sick) => {
                                sick.concomitant_symptom.forEach((item) => {
                                    if (!list.find((it) => it.name === item.symptomName)) {
                                        list.push({
                                            id: item.symptomName,
                                            name: item.symptomName,
                                        });
                                    }
                                });
                            });
                        }
                    } else if (this.client === 'tcm-therapy') {
                        // 治法
                        this.sickness.forEach((sick) => {
                            sick.therapy_exam.forEach((item) => {
                                if (!list.find((it) => it.name === item.symptomName)) {
                                    list.push({
                                        id: item.symptomName,
                                        name: item.symptomName,
                                    });
                                }
                            });
                        });
                    } else if (this.client === 'tcm-prescription') {
                        // 方药
                        this.sickness.forEach((sick) => {
                            sick.prescription_exam.forEach((item) => {
                                if (!list.find((it) => it.name === item.symptomName)) {
                                    list.push({
                                        name: item.symptomName,
                                        prescriptionFormItems: item.prescriptionFormItems,
                                    });
                                }
                            });
                        });
                    } else {
                        this.sickness.map((item) => {
                            if (!list.find((it) => it.name === item.diseaseName)) {
                                list.push({
                                    id: item.diseaseGuid,
                                    name: item.diseaseName,
                                    diseaseStdCode: item.disease_std_code,
                                });
                            }
                        });
                    }
                    if (list.length) {
                        // eslint-disable-next-line vue/no-async-in-computed-properties
                        this.$nextTick(() => {
                            this.initPopper();
                        });
                    }
                    return list;
                }
                if (
                    this.client === 'tcm-syndrome' ||
                    this.client === 'tcm-therapy' ||
                    this.client === 'tcm-prescription'
                ) {
                    return [];
                }
                if (this.diseases.length) {
                    // eslint-disable-next-line vue/no-async-in-computed-properties
                    this.$nextTick(() => {
                        this.initPopper();
                    });
                }
                return this.diseases.map((item) => {
                    return {
                        id: item.diseaseGuid,
                        name: item.diseaseName,
                        diseaseStdCode: item.disease_std_code,
                    };
                });


            },
        },

        watch: {
            value: {
                handler (val) {
                    if (!val) {
                        this.asyncValue = '';
                        this.suggestions = [];
                        this.currentIndex = -1;
                    }
                    this.initCurrentList(val);
                },
                immediate: true,
            },
            currentValue() {
                this.updateStyle();
            },
        },
        created() {
            // 注册防抖search函数
            this._debounceSearch = debounce(this.queryDiagnosisAsync, 200, true);
            this._reg = /[\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|,|，|、|.|:|;]/g;
        },
        methods: {
            initCurrentList(val) {
                if (isJSON(val)) {
                    this.currentList = JSON.parse(val);
                    this.trans2Str();
                } else {
                    this.trans2Json(val);
                    this.trans2Str();
                }
            },

            trans2Json(val) {
                val = val || '';
                val = val.replace(/\u200b/g, '');
                const _arr = val.split(/<i contenteditable="false">，<\/i>/);
                this.currentList = _arr.filter((it) => it);
            },

            trans2Str() {
                this.currentValue = this.currentList.join('<i contenteditable="false">，</i>');
            },

            handleClick() {
                if (this.disabled) return false;
                this.updateStyle();
                if (this.needAideSuggestions) {
                    this.suggestions = this.aides;
                }
                this.showSuggestions = true;
                this.currentIndex = 0;
            },
            handleTab() {
                this.showSuggestions = false;
            },
            handleFocus(e) {
                this.isFocusing = true;

                if (this.currentValue) {
                    this.currentValue = this.currentValue.replace('<i contenteditable="false">，</i>\u200b', '');
                    this.currentValue += '<i contenteditable="false">，</i>\u200b';
                    this.$nextTick(() => {
                        const { forceUpdate } = this.$refs['ref-target'];
                        if (typeof forceUpdate === 'function') {
                            forceUpdate();
                        }
                    });
                    this._timer = setTimeout(() => {
                        keepLastIndex(e.target);
                    }, 1);
                }
                // 记录最后一次的值，后续用于对比
                this._lastEditValue = this.currentValue;
            },
            async handleBlur() {
                this.isFocusing = false;
                if (this.currentValue) {
                    this.currentValue = this.currentValue.replace(/<i contenteditable="false">，<\/i>\u200b?$/, '');
                }
                this.emitDataHandler();
                this._timer && clearTimeout(this._timer);
            },

            handleInput(val) {
                this.currentValue = val.replace(/(<i contenteditable="false">，<\/i>)*(\u200b)*$/, '');
                this.trans2Json(this.currentValue);
                this._debounceSearch();
            },

            // click outside 回调方法
            outside() {
                this.showSuggestions = false;
            },

            updateStyle() {
                this.suggestionsStyle = {
                    top: this.fixed ? '0' : `${this.$refs['ref-target'].$refs.abcinput.offsetHeight + 4}px`,
                };
            },

            handleDown(e) {
                if (this.showSuggestions && this.suggestions.length) {
                    if (e.isComposing) {
                        e.preventDefault();
                        e.stopPropagation();
                        return;
                    }
                    if (this.currentIndex < this.suggestions.length - 1) {
                        this.currentIndex++;
                    }
                    e.stopPropagation();
                    this.changeHighLight();
                    return;
                }
                this.showSuggestions = false;
            },
            handleUp(e) {
                if (this.showSuggestions && this.suggestions.length) {
                    if (e.isComposing) {
                        e.preventDefault();
                        e.stopPropagation();
                        return;
                    }
                    if (this.currentIndex > 0) {
                        this.currentIndex--;
                    }
                    e.stopPropagation();
                    this.changeHighLight();
                    return;
                }
                this.showSuggestions = false;
            },
            changeHighLight() {
                const index = this.currentIndex;
                if (index < 0) return false;
                const suggestions = this.$refs['popper-target'];
                const suggestionList = suggestions.querySelectorAll('.suggestions-item');

                const highlightItem = suggestionList[index];
                const { scrollTop } = suggestions;
                const { offsetTop } = highlightItem;

                const { scrollHeight } = highlightItem;

                if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestions.clientHeight)) {
                    suggestions.scrollTop += scrollHeight;
                }
                if (offsetTop < scrollTop) {
                    suggestions.scrollTop -= scrollHeight;
                }
            },
            enterEvent(e) {
                e.preventDefault && e.preventDefault();
                if (this.showSuggestions && this.suggestions.length) {
                    this.$emit('enterSelect', e);
                    if (this.currentIndex > -1) {
                        const _obj = this.suggestions[this.currentIndex];
                        this.selectAideDisease(_obj);
                    }
                    return false;
                }
            },

            async queryDiagnosisAsync() {
                const _arr = this.currentValue.split('<i contenteditable="false">，</i>');
                this.asyncValue = _arr[_arr.length - 1] || '';
                this.asyncValue = this.asyncValue.replace(/\u200b|<\/?br>/g, '');
                if (this.asyncValue.trim() && this.asyncValue.length < 100) {
                    if (this.client === 'tcm-prescription') {
                        const { data } = await OutpatientAPI.searchCatalogue(
                            'prescription',
                            {
                                keyword: this.asyncValue,
                                offset: 0,
                                limit: 10,
                                scope: 3,
                                notWithClinicalPrescription: 1,
                            },
                        );
                        this.suggestions = data.rows;
                    } else if (this.client) {
                        const { data } = await CDSSAPI.doSearch({
                            keyword: this.asyncValue,
                            client: this.client,
                            offset: 0,
                            limit: 10,
                        });
                        if (data.hits && this.asyncValue.trim()) {
                            const { length } = data.hits;
                            data.hits.length = length > 10 ? 10 : length;
                            this.suggestions = data.hits.map((item) => {
                                return {
                                    id: item.name,
                                    name: item.name,
                                };
                            });
                        } else {
                            this.suggestions = [];
                        }
                    } else {

                        const { data } = await SocialAPI.searchSocialDiagnosis({
                            keyword: this.asyncValue,
                        });
                        const { diagnosisInfos } = data;
                        if (diagnosisInfos && diagnosisInfos.length && this.asyncValue.trim()) {
                            diagnosisInfos.length = diagnosisInfos.length > 10 ? 10 : diagnosisInfos.length;
                            this.suggestions = diagnosisInfos.map((item) => {
                                return {
                                    name: item.name,
                                    code: item.code,
                                    diseaseType: item.diseaseType,
                                    hint: item.hint,
                                };
                            });
                        } else {
                            this.suggestions = [];
                        }
                    }
                } else {
                    this.suggestions = [];
                }
                if (this.suggestions.length) {
                    this.currentIndex = 0;
                    this.showSuggestions = false;
                    this.$nextTick(() => {
                        this.showSuggestions = true;
                    });
                } else {
                    this.currentIndex = -1;
                }
            },

            selectAideDisease(aide, replaceSearchVal = true) {
                const {
                    name,
                } = aide;
                if (!name) return;

                if (this.asyncValue && replaceSearchVal) {
                    try {
                        const reg = new RegExp(`${this.asyncValue}(</?br>)*$`);
                        this.currentValue = this.currentValue.replace(reg, '');
                        this.trans2Json(this.currentValue);
                    } catch (e) {
                        console.error('存在特殊符号');
                    }
                    this.asyncValue = '';
                }

                if (this.showSuggestions && this.suggestions.length) {
                    this.suggestions = [];
                    this.forceUpdateFlag = 1;
                    this.$nextTick(() => {
                        this.forceUpdateFlag = 0;
                    });
                }

                this.currentList.push(`${name}`);
                this.trans2Str();
                this.emitDataHandler();

                this.asyncValue = '';
                this.suggestions = [];
                this.showSuggestions = false;
                this.$emit('changeDisease', aide);
            },

            emitDataHandler() {
                this.$emit('input', this.currentValue);
                this.formItem && this.formItem.$emit('formFieldInput', this.currentValue);
            },
        },
    };
</script>
