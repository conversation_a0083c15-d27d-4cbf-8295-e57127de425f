<template>
    <div class="outpatient-form-item medical-record-module-wrapper" :class="{ 'is-disabled': disabled }" data-cy="medical-record-module">
        <div class="title" style="padding-right: 7px;">
            <h3 style="height: 32px;">
                {{ medicalRecordTitle }}
            </h3>

            <div v-show="!disabledEdit || isOpenSource" class="btn-group">
                <template v-if="isEnableAiVoiceMr && !disabledEdit && !isIntranetUser">
                    <voice-record-trigger-button
                        :switch-setting="switchSetting"
                        :patient-info="patientInfo"
                        :outpatient-info="postData"
                        :has-asr-result.sync="hasAsrResult"
                    ></voice-record-trigger-button>
                    <div class="cut-line" style="margin-right: 8px;"></div>
                </template>
                <template v-if="isSupportMedicalDocument">
                    <medical-document-quick-create
                        v-show="!disabledEdit"
                        ref="medicalDocumentQuickCreate"
                        :department-id="postData.departmentId"
                        :business-type="MedicalDocumentBusinessType.OUTPATIENT"
                        :business-id="postData.id"
                        :patient-info="patientInfo"
                        :patient-order-id="postData.patientOrderId"
                        :medical-document-list.sync="medicalDocumentList"
                        :post-data="postData"
                    >
                        <abc-button
                            variant="ghost"
                            theme="default"
                            size="small"
                            data-cy="mr-add-document"
                        >
                            添加文书
                        </abc-button>
                    </medical-document-quick-create>
                </template>

                <template v-if="!disabledEdit">
                    <abc-check-access :access-key="accessMap.QUESTION_FORM">
                        <abc-button
                            variant="ghost"
                            theme="default"
                            size="small"
                            data-cy="mr-question-form-btn"
                            style="margin-left: 6px;"
                            @click="createQuestionForm"
                        >
                            问诊单
                        </abc-button>
                    </abc-check-access>

                    <abc-button
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="mr-upload-btn"
                        style="margin-left: 6px;"
                        @click="expandEReport"
                    >
                        上传附件
                    </abc-button>

                    <div class="cut-line"></div>
                    <outpatient-history-popover
                        v-if="_showHistoryMR && selectedPatient && selectedPatient.id"
                        v-model="showOutpatientHistoryPopover"
                        :patient-id="selectedPatient.id"
                        @select-outpatient-record="selectMedicalRecord"
                    >
                        <abc-button
                            variant="ghost"
                            theme="default"
                            size="small"
                            data-cy="mr-history-btn"
                            style="margin-left: 6px;"
                            @click="handlerClickMrHistory"
                        >
                            历史病历{{ recordCount }}
                        </abc-button>
                    </outpatient-history-popover>
                    <abc-button
                        v-if="isShowMRTemplateButton"
                        variant="ghost"
                        theme="default"
                        size="small"
                        icon="s-b-data-1-line"
                        style="margin-left: 6px;"
                        data-cy="mr-template-btn"
                        @click="showMRTemplateDialog = true"
                    >
                        病历模板
                    </abc-button>
                </template>
                <abc-check-access
                    v-if="isShowMRTemplateSaveButton"
                    :permission-keys="[ROLE_DOCTOR_ID, ROLE_DOCTOR_ASSIST_ID]"
                >
                    <abc-button
                        v-if="isOpenSource"
                        icon="save"
                        variant="text"
                        theme="default"
                        size="small"
                        data-cy="add-mr-template-btn"
                        class="add-mr-template-btn"
                        style="margin-left: 6px;"
                        @click="saveCommon"
                    >
                    </abc-button>
                </abc-check-access>
                <component
                    :is="currentMedicalSettingPopover"
                    v-if="!disabledEdit"
                    :revisit-status="postData.revisitStatus"
                    class="medical-setting-popover-btn"
                    style="margin-left: 6px;"
                    :disabled="disabledMRSetting"
                    @change="changeSettingHandler"
                ></component>
            </div>
        </div>

        <component
            :is="currentMedicalRecord"
            ref="medicalRecord"
            v-model="postData.medicalRecord"
            :post-data="postData"
            :disabled="disabled"
            :readonly="readonly"
            :status="status"
            :is-childcare="isChildcare"
            :is-open-source="isOpenSource"
            :disabled-diagnosis="disabledDiagnosis"
            :only-agent="isChildcare ? false : onlyAgent"
            :medical-document-list="medicalDocumentList"
            :from-module="fromModule"
            :switch-setting="switchSetting"
            :product-forms="productForms"
            :disabled-update-question-form="disabledUpdateQuestionForm"
            :must-match-disease-code="$abcSocialSecurity.isSupportMatchDiseaseCode"
            :must-at-least-one-western-disease="mustAtLeastOneWesternDisease"
            :show-doctor-advice="showDoctorAdvice"
            :diagnosis-label="diagnosisLabel"
            :sorted-mr-struct="sortedMrStruct"
            @add-prescription="(val) => $emit('add-prescription', val)"
            @delete-questionForm="$emit('delete-questionForm')"
            @medical-document-click="handleMedicalDocumentClick"
        ></component>

        <div>
            <!--这个div千万不能删除，原因咨询jason-->
        </div>

        <medical-record-template-dialog
            v-if="showMRTemplateDialog"
            v-model="showMRTemplateDialog"
            :owner-type="ownerType"
            :version="templateManagerVersion"
            @useTemplate="handleUseTemplate"
        ></medical-record-template-dialog>

        <common-add-template-dialog
            v-if="showAddDialog"
            v-model="showAddDialog"
            :source-data="postData"
            template-type="medicalrecord"
        ></common-add-template-dialog>
    </div>
</template>

<script>
    import FormAPI from 'src/api/form/index';
    import OutpatientAPI from 'src/api/outpatient';
    import CrmAPI from 'src/api/crm';
    import Logger from 'utils/logger';

    const MedicalRecordTemplateDialog = () => import('../common/medical-template-dialog');
    import OutpatientHistoryPopover from 'src/views/outpatient/common/outpatient-history-popover';
    import createSelectProjectDialog from 'src/views/chronic-care/select-project-dialog';
    import GenerateFormDialog from 'src/views/chronic-care/form-dialog/index';
    import CommonAddTemplateDialog from 'views/layout/templates-manager/common-add-template-dialog';

    import { mapGetters } from 'vuex';
    import {
        ROLE_DOCTOR_ASSIST_ID,
        ROLE_DOCTOR_ID,
    } from 'utils/constants';
    import { ChronicCareFormPrintType } from 'views/chronic-care/constants';
    import AbcSocket from 'views/common/single-socket.js';

    import { getPermissionObjByRoles } from 'views/permission/uitls.js';
    import AbcAccess from '@/access/utils.js';
    import { OutpatientStatusEnum } from 'views/outpatient/constants';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants.js';
    import { RevisitStatus } from 'src/assets/configure/constants';
    import MedicalDocumentListPopover from 'views/outpatient/common/medical-document-list-popover.vue';
    import MedicalDocumentQuickCreate from '@/views-hospital/nursing/components/medical-document-quick-create.vue';
    import {
        getMRStructList,
    } from 'views/outpatient/common/medical-record/utils';
    import {
        getMRComponentByType,
    } from '@/views-hospital/outpatient/utils/index.js';
    import {
        getVoiceRecordModule,
    } from '@/module-federation-dynamic/deepseek';

    export default {
        name: 'MedicalRecordModule',

        components: {
            MedicalDocumentQuickCreate,
            MedicalRecordTemplateDialog,
            OutpatientHistoryPopover,
            MedicalDocumentListPopover,
            CommonAddTemplateDialog,
            VoiceRecordTriggerButton: () => ({
                // eslint-disable-next-line no-async-promise-executor
                component: new Promise(async (resolve) => {
                    const voiceRecordModule = await getVoiceRecordModule();
                    resolve(voiceRecordModule?.default?.VoiceRecordTriggerButton);
                }),
            }),
        },

        props: {
            postData: {
                type: Object,
                required: true,
            },
            disabled: Boolean,
            readonly: Boolean,
            switchSetting: {
                type: Object,
            },
            ownerType: {
                type: Number,
                default: 3,
            },
            status: Number,
            isChildcare: {
                type: Boolean,
                default: false,
            },
            disabledDiagnosis: {
                type: Boolean,
                default: false,
            },
            selectedPatient: {
                type: Object,
            },
            disabledUpdateQuestionForm: {
                type: Boolean,
                default: false,
            },
            productForms: {
                type: Array,
                default: () => [],
            },
            isSupportMedicalDocument: {
                type: Boolean,
                default: false,
            },
            // 病历标题是否显示初复诊
            showRevisitTitle: {
                type: Boolean,
                default: false,
            },
            diagnosisLabel: {
                type: String,
                default: '诊断',
            },
            showDoctorAdvice: {
                type: Boolean,
                default: true,
            },
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            mustAtLeastOneWesternDisease: {
                type: Boolean,
                default: false,
            },
            fromModule: {
                type: String,
                default: 'outpatient',
            },
            sortedMrStruct: Array,
            // 是否展示病历模板按钮
            isShowMRTemplateButton: {
                type: Boolean,
                default: true,
            },
            // 是否展示病历模板保存按钮
            isShowMRTemplateSaveButton: {
                type: Boolean,
                default: true,
            },
            // 是否禁用病历设置
            disabledMRSetting: Boolean,
        },

        data() {
            return {
                ROLE_DOCTOR_ID,
                ROLE_DOCTOR_ASSIST_ID,
                accessMap: AbcAccess.accessMap,
                showEReport: false,
                showMRTemplateDialog: false,
                showOutpatientHistoryPopover: false,
                medicalDocumentList: [],
                MedicalDocumentBusinessType,
                showAddDialog: false,
                focusoutTimeoutId: null, // 添加一个属性来存储timeout ID
                hasAsrResult: false, // 是否有语音识别结果
            };
        },

        computed: {
            ...mapGetters(['userInfo', 'currentClinic', 'isEnableAiVoiceMr', 'isIntranetUser']),
            ...mapGetters('viewDistribute', [
                'viewComponents',
                'viewDistributeConfig',
            ]),
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            ...mapGetters('examination', ['isEnableCloudSupplier' ]),

            isEnableAiSearch() {
                return this.isEnableCloudSupplier;
            },
            disabledEdit() {
                return this.disabled || this.readonly;
            },
            curMedicalRecordType() {
                return this.postData?.medicalRecord?.type;
            },
            currentMedicalRecord() {
                return getMRComponentByType(this.curMedicalRecordType);
            },
            currentMedicalSettingPopover() {
                return this.viewComponents.medicalSettingPopover;
            },
            patientInfo() {
                return this.postData.patient || {};
            },
            chainId() {
                return this.currentClinic && this.currentClinic.chainId;
            },
            onlyAgent() {
                const { roleIds } = this.userInfo || {};
                if (!roleIds) {
                    return false;
                }
                const permissionObj = getPermissionObjByRoles(roleIds);
                return roleIds.indexOf(ROLE_DOCTOR_ASSIST_ID) > -1 && !permissionObj.canDiagnosis;
            },
            recordCount() {
                let str = '';
                if (this.selectedPatient && this.selectedPatient.outPatientTimes) {
                    str = `(${this.selectedPatient.outPatientTimes})`;
                }
                return str;
            },

            patientId() {
                return this.postData.patient && this.postData.patient.id;
            },

            medicalRecordTitle() {
                if (!this.showRevisitTitle) return '病历';
                if (!this.postData.revisitStatus) return '病历';
                return this.postData.revisitStatus === RevisitStatus.FIRST ? '初诊病历' : '复诊病历';
            },
        },

        watch: {
            'postData.patient.id': {
                async handler(newPatientId) {
                    // 待诊或者草稿状态需要拉取患者临时附件
                    if (newPatientId && [OutpatientStatusEnum.DRAFT, OutpatientStatusEnum.UN_DIAGNOSIS].includes(this.postData.status)) {
                        const { data } = await OutpatientAPI.fetchTempAttachments(newPatientId);
                        if (Array.isArray(data) && data.length) {
                            const currentAttachments = this.postData.medicalRecord.attachments;
                            this.postData.medicalRecord.attachments = data.map((it) => {
                                return {
                                    ...it,
                                    temp: true,
                                };
                            }).concat(currentAttachments);
                        } else {
                            this.postData.medicalRecord.attachments = this.postData.medicalRecord.attachments?.filter((it) => !it.temp) ?? [];
                        }
                    }
                },
            },
        },
        mounted() {
            this.$nextTick(() => {
                this.bindMedicalRecordFocusoutEvent();
            });
        },
        beforeDestroy() {
            this._socket.off('outpatient.new_attachment.img', this.handleImages);

            // 清除之前的timeout
            if (this.focusoutTimeoutId) {
                clearTimeout(this.focusoutTimeoutId);
                this.focusoutTimeoutId = null;
            }

            // 清除缓存的输入框元素
            this._cachedInputElements = null;

            // 移除事件监听
            if (this.$refs.medicalRecord && this.$refs.medicalRecord.$el && this.isEnableAiSearch) {
                this.$refs.medicalRecord.$el.removeEventListener('focusout', this.handleFocusoutEvent);
            }
        },
        created() {
            const { showHistoryMR } = this.viewDistributeConfig.Outpatient;
            this._showHistoryMR = showHistoryMR;

            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('outpatient.new_attachment.img', this.handleImages);


            this.$abcEventBus.$on('GENERAL_INSPECTION', (payload) => {
                if (this.disabledEdit) return;
                const { str } = payload;
                console.log('AbcGeneralInspectionService: GENERAL_INSPECTION', str);
                this.postData.medicalRecord.physicalExamination = (this.postData.medicalRecord.physicalExamination || '') + str;
            }, this);
        },
        methods: {
            handleFocusoutEvent() {
                // 清除之前的timeout，防止多次触发
                if (this.focusoutTimeoutId) {
                    clearTimeout(this.focusoutTimeoutId);
                    this.focusoutTimeoutId = null;
                }

                // 确保组件引用仍然存在
                if (!this.$refs.medicalRecord || !this.$refs.medicalRecord.$el) {
                    return;
                }

                const medicalRecordEl = this.$refs.medicalRecord.$el;
                this.focusoutTimeoutId = setTimeout(() => {
                    // 清除timeout ID引用
                    this.focusoutTimeoutId = null;

                    // 确保组件仍然存在（没有被销毁）
                    if (!this.$refs.medicalRecord || !this.$refs.medicalRecord.$el) {
                        return;
                    }

                    // 获取所有包含abc-input__inner类的输入框
                    const inputElements = this._cachedInputElements || medicalRecordEl.querySelectorAll('.abc-input__inner');

                    // 判断焦点是否在这些输入框中
                    const { activeElement } = document;
                    let focusInInputs = false;

                    // 检查当前活动元素是否在输入框列表中
                    for (let i = 0; i < inputElements.length; i++) {
                        if (inputElements[i] === activeElement) {
                            focusInInputs = true;
                            break;
                        }
                    }

                    // 检查焦点是否在快速选项面板中
                    if (!focusInInputs) {
                        const quickOptionsPanel = document.querySelector('.quick-options-panel-wrapper');
                        if (quickOptionsPanel) {
                            focusInInputs = true;
                        }
                    }

                    // 如果焦点不在输入框中，则触发blur-medical-document事件
                    if (!focusInInputs) {
                        this.$abcEventBus.$emit('get-ai-examination-suggestion');
                    }
                }, 200);
            },
            /**
             * @desc 绑定医疗记录DOM元素的focusout事件
             * <AUTHOR>
             * @date 2025-05-14
             */
            bindMedicalRecordFocusoutEvent() {
                if (this.isEnableAiSearch) {
                    if (this.$refs.medicalRecord && this.$refs.medicalRecord.$el) {
                        // 添加事件监听
                        const medicalRecordEl = this.$refs.medicalRecord.$el;
                        medicalRecordEl.addEventListener('focusout', this.handleFocusoutEvent);

                        // 缓存输入框元素
                        this._cachedInputElements = medicalRecordEl.querySelectorAll('.abc-input__inner');
                    }
                }
            },

            handleImages(data) {
                const {
                    attachments = [], outpatientSheetId = '',
                } = data;
                const isTempAttachmentsUpdate = this.patientId && [OutpatientStatusEnum.DRAFT, OutpatientStatusEnum.UN_DIAGNOSIS].includes(this.postData.status) && data.patientId === this.patientId;
                Logger.report({
                    scene: 'outpatient.new_attachment.img',
                    data: {
                        attachments,
                        outpatientSheetId,
                        localId: this.postData.id,
                        isTempAttachmentsUpdate,
                        localPatientId: this.patientId,
                        patientId: data.patientId,
                    },
                });
                if ((this.postData.id === outpatientSheetId || isTempAttachmentsUpdate) && attachments.length > 0) {
                    attachments.forEach((item) => {
                        const isExist = this.postData.medicalRecord.attachments.find((it) => it.id === item.id);
                        if (!isExist) {
                            if (isTempAttachmentsUpdate) {
                                item.temp = true;
                            }
                            this.postData.medicalRecord.attachments.push(item);
                        }
                    });
                }
            },
            /**
             * @desc 在辅助检查中点击上传图片，展开上传图片组件and触发第一次选择
             * <AUTHOR>
             * @date 2019/06/13 15:27:53
             * @params
             * @return
             */
            expandEReport() {
                this.showEReport = true;
                this.postData.medicalRecord.attachments = this.postData.medicalRecord.attachments || [];
                this.$nextTick(() => {
                    this.$refs.medicalRecord.$refs.eReport.triggerFileInput();
                });
            },

            createQuestionForm() {
                if (this._loadingData) return false;
                if (!this.validatePatientInfo()) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '请填写患者信息',
                    });
                    return false;
                }
                if (!this.patientId) {
                    const {
                        name, age, sex, mobile,
                    } = this.postData.patient;
                    let _str = `${name} ${sex}`;
                    if (age.year) {
                        _str += ` ${age.year}岁`;
                    }
                    if (age.month) {
                        _str += ` ${age.month}月`;
                    }
                    _str += `  ${mobile}`;
                    this.$confirm({
                        title: '是否创建新患者，并填写问诊单？',
                        content: [_str],
                        onConfirm: async () => {
                            await this.createPatient();
                            this.selectQuestionForm();
                        },
                    });
                    return false;
                }
                this.selectQuestionForm();
            },

            validatePatientInfo() {
                const {
                    name, age, sex,
                } = this.postData.patient;
                if (!name || !sex) {
                    return false;
                }
                if (!age) {
                    return false;
                }
                if (!age.year && !age.month) {
                    return false;
                }
                return true;
            },

            async selectQuestionForm() {
                this._loadingData = true;
                const { data } = await FormAPI.fetchFormTemplates({
                    type: 1,
                });
                createSelectProjectDialog({
                    title: '选择问诊单',
                    projects: data.rows,
                    selectFunction: this.selectFormHandle,
                    closeFunction: () => {
                        this._selectDialog = null;
                    },
                });
                this._loadingData = false;
            },

            /**
             * @desc 创建患者
             * <AUTHOR> Yang
             * @date 2021-01-15 14:39:13
             */
            async createPatient() {
                try {
                    const { data } = await CrmAPI.insertPatientInfo(this.postData.patient);
                    Object.assign(this.postData.patient, data);
                } catch (e) {
                    console.error(e);
                }
            },

            selectFormHandle(form) {
                const {
                    id, name, sex, age, mobile, wxBindStatus, wxStatus,
                } = this.postData.patient;
                GenerateFormDialog({
                    formTemplateId: form.id,
                    patient: {
                        id,
                        name,
                        sex,
                        age,
                        mobile,
                        wxBindStatus,
                        wxStatus,
                    },
                    submitFunction: this.createOutpatientQuestionForm,
                    chainId: this.chainId,
                    pushFunction: (formData, callback) => {
                        this.$confirm({
                            title: '提示',
                            content: '将推送至患者微信。填写完成后，可在门诊页面查看结果。是否确认推送？',
                            onConfirm: () => {
                                this.pushOutpatientQuestionForm(formData, callback);
                            },
                            onClose: () => {
                                callback();
                            },
                        });
                    },
                    printFormType: ChronicCareFormPrintType.CONSULTATION, // 1 问诊单 2 档案 3 评估表
                });
            },
            async createOutpatientQuestionForm(formData, callback) {
                try {
                    const formId = formData.id;
                    delete formData.id;
                    const { data } = await OutpatientAPI.createOutpatientQuestionForm(this.postData.id, {
                        formId,
                        ...formData,
                    });
                    this.postData.questionSheets.push({
                        id: data.id,
                        name: data.form.name,
                    });
                    callback(data);
                } catch (e) {
                    callback();
                    console.error(e);
                }
            },

            async pushOutpatientQuestionForm(formData, callback) {
                try {
                    const { data } = await OutpatientAPI.pushOutpatientQuestionForm(this.postData.id, {
                        formId: formData.id,
                        patientId: this.patientId,
                    });
                    callback(data);
                    this.$Toast({
                        message: '推送成功',
                        type: 'success',
                    });
                } catch (e) {
                    callback();
                }
            },

            selectMedicalRecord(data) {
                this.$emit('select-outpatient-record', data);
            },

            handleUseTemplate(data) {
                // 长护门诊单不能修改诊断信息
                if (this.disabledDiagnosis) {
                    data.extendDiagnosisInfos = this.postData.medicalRecord.extendDiagnosisInfos;
                }
                this.$emit('useTemplate', data);
            },
            changeSettingHandler(data) {
                this.$abcEventBus.$emit('medical-record-struct-change', data);
            },
            handleMedicalDocumentClick(item) {
                this.$refs.medicalDocumentQuickCreate.handleMedicalDocumentClick(item);
            },
            saveCommon() {
                if (getMRStructList(this.postData.medicalRecord).length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '病历为空，不能保存为模板',
                    });
                    return false;
                }
                this.showAddDialog = true;
            },
            handlerClickMrHistory() {
                this.showOutpatientHistoryPopover = !this.showOutpatientHistoryPopover;
                Logger.report({
                    scene: 'outpatient.mr.history',
                    data: {
                        subScene: 'clinic',
                    },
                });
            },
        },
    };
</script>

<style lang="scss">
.medical-record-module-wrapper {
    .add-mr-template-btn + .medical-setting-popover-btn {
        margin-left: 0 !important;
    }
}
</style>
