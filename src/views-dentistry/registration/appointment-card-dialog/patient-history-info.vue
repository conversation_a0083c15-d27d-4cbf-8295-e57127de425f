<template>
    <div
        :class="{
            'patient-info-history-wrapper': true,
            'edit-status': editor,
            'display-history-wrapper': tabValue === 1,
        }"
    >
        <div ref="contentWrapper" class="content-wrapper" :class="{ 'overflow-y-hidden': showLiItem }">
            <div class="header">
                <div class="left">
                    <abc-tabs
                        v-model="tabValue"
                        size="small"
                        :custom-gap="28"
                        disable-indicator
                        :border-style="{ borderBottom: 'none' }"
                        :option="tabsOption"
                    ></abc-tabs>
                </div>
                <div v-if="tabValue === 0" class="right">
                    <abc-button
                        v-if="!editor"
                        type="blank"
                        size="small"
                        @click="handleEdit"
                    >
                        编辑
                    </abc-button>
                    <abc-space v-else>
                        <abc-button
                            size="small"
                            :disabled="noChangeData"
                            :loading="saveLoading || changeCascaderLoading"
                            @click="handleSave"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            type="blank"
                            size="small"
                            @click="handleCancel"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </div>
            <div v-if="!editor" class="display-split-line"></div>
            <div class="content">
                <div v-if="tabValue === 0" v-abc-loading="loading" class="patient-info-tab">
                    <!-- 标签和就诊信息 -->
                    <abc-tag-group v-if="!editor" class="tag-wrapper">
                        <img
                            v-if="displayMember"
                            class="tag-wrapper_logo"
                            src="~assets/images/crm/v-yellow-square.png"
                            alt="vip"
                        />
                        <img
                            v-if="displayCharge"
                            class="tag-wrapper_logo"
                            src="~assets/images/crm/<EMAIL>"
                            alt="arrears"
                        />
                        <img
                            v-if="displayWechat"
                            class="tag-wrapper_logo"
                            src="~assets/images/crm/wechat.png"
                            alt="wechat"
                        />
                        <img
                            v-if="displayQiWei"
                            class="tag-wrapper_logo"
                            src="~assets/images/crm/qiwei.png"
                            alt="qiwei"
                        />
                        <div
                            v-for="tag in showTags"
                            :key="tag.tagId"
                        >
                            <tag-item
                                :tag="tag"
                                @click-close-label="clickCloseLabel"
                            ></tag-item>
                        </div>
                        <abc-popover
                            ref="abc-pop-tag"
                            trigger="manual"
                            :value="showTagPopover"
                            :visible-arrow="false"
                            popper-class="patient-label-tags__popper"
                            placement="bottom-start"
                            theme="white"
                        >
                            <abc-tags-add slot="reference" style="height: 20px;" @click.native="showTagPopover = !showTagPopover"></abc-tags-add>
                            <div v-abc-click-outside="outside" class="box">
                                <view-labels
                                    :show-manage="false"
                                    :selected-ids="selectedIds"
                                    :filter="false"
                                    :disabled-hover="true"
                                    @change="clickHandleTag"
                                ></view-labels>
                            </div>
                        </abc-popover>
                    </abc-tag-group>
                    <div class="outpatient-statistics-wrapper">
                        <span>
                            门诊 {{ patientBaseInfo.outpatientCount || 0 }} 次<template v-if="isShowHistoryFee">
                                ，消费 {{ $t('currencySymbol') }}{{ formatMoney(patientBaseInfo.cumulativeAmount) }}
                            </template>
                            <span v-if="isNeedDisCharge" class="owe-pay">{{ `（欠费 ${$t('currencySymbol')}${ formatMoney(patientBaseInfo.owePayedAmount)}）` }}</span>
                        </span>
                    </div>
                    <!-- 标签和就诊信息 -->
                    <div class="patient-info-wrapper" :class="{ 'is-editor-status': editor }">
                        <crm-patient-basic-info
                            ref="crmPatientBasicInfo"
                            :key="patientInfo && patientInfo.id"
                            :post-data-info.sync="postData"
                            :editor.sync="editor"
                            to-element=".patient-info-history-wrapper"
                            teleport-value=".patient-info-dialog-wrapper"
                            :no-change-data-info.sync="noChangeData"
                            :default-patient="patientInfo"
                            :patient-basic-info.sync="patientInfo"
                            :show-li-item.sync="showLiItem"
                            :not-id-show-detail="notIdShowDetail"
                            :is-add-patient="isAddPatient"
                            :cover-top.sync="coverTop"
                            :is-can-see-patient-mobile="isCanSeePatientMobileInRegistration"
                            :cover-height.sync="coverHeight"
                            :visible-qr-code-info.sync="visibleQrCode"
                            :visible-unbind-w-x-info.sync="visibleUnbindWX"
                            @handleCommon="handleCommon"
                            @confirm="confirm"
                        ></crm-patient-basic-info>
                        <template v-if="patientId && isOpenSocial">
                            <abc-divider
                                size="normal"
                                theme="light"
                                variant="dashed"
                                margin="small"
                            ></abc-divider>
                            <div class="crm-patient-item-container-wrapper-shebao">
                                <crm-shebao-info :shebao-info="shebaoCardInfo" :visible-social-card-info.sync="visibleSocialCardInfo"></crm-shebao-info>
                            </div>
                        </template>
                        <div v-if="patientId" class="crm-patient-item-container-wrapper">
                            <abc-divider
                                size="normal"
                                theme="light"
                                variant="dashed"
                                margin="small"
                            ></abc-divider>
                            <crm-patient-item-container
                                :patient-id="patientId"
                                :patient-basic-info.sync="patientInfo"
                                :show-li-item.sync="showLiItem"
                                :max-height="maxHeight"
                                teleport-value=".patient-info-dialog-wrapper"
                                @visible-family-doctor="visibleFamilyDoctor = $event"
                                @visible-bind-files="visibleBindFiles = $event"
                                @visible-card-info="visibleCardInfo = $event"
                                @handle-common="handleCommon"
                                @handle-refresh="fetchPatientOverview"
                            ></crm-patient-item-container>
                        </div>
                    </div>
                    <div
                        v-if="isShowLiItem"
                        class="cover"
                        :style="{
                            height: `${coverHeight}px` ,top: !!coverTop && `${coverTop}px`
                        }"
                    ></div>

                    <!--   用于子组件传送内部组件展示在父组件中（水平垂直居中）    -->
                    <div :class="['patient-info-dialog-wrapper', { 'dialog-wx-box': visibleQrCode }]" :style="{ 'margin-top': `${coverTop}px` }">
                    </div>
                </div>
                <div v-show="tabValue === 1" style="position: relative;">
                    <patient-outpatient-history
                        ref="patient-outpatient-history"
                        :patient-info="patientInfo"
                        from-module="appointment"
                        :show-operation-btn-group="false"
                        @changeTotalCount="handleTotalCount"
                    >
                        <abc-content-empty slot="customEmpty" top="100px" value="暂无数据"></abc-content-empty>
                    </patient-outpatient-history>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import PatientOutpatientHistory from 'views/layout/patient-outpatient-history.vue';
    import CrmAPI from 'api/crm';
    import {
        mapState, mapGetters, mapActions,
    } from 'vuex';
    import ViewLabels from 'views/crm/common/package-label/view-labels.vue';
    import AbcTagsAdd from 'views/crm/component/abc-tags-add.vue';
    import TagItem from 'views/crm/patient-files/card-patient-overview/tag-item.vue';
    import CrmPatientBasicInfo from 'views/layout/patient/crm-patient-basic-info.vue';
    import clone from 'utils/clone';
    import { WxBindStatusEnum } from '@abc/constants';
    import MixinModulePermission from 'views/permission/module-permission';
    import CrmPatientItemContainer from 'views/layout/patient/crm-patient-item-container.vue';
    import CrmShebaoInfo from 'views/layout/patient/crm-shebao-info.vue';
    import { formatMoney } from '@/utils';
    import Clone from 'utils/clone';
    import { DEFAULT_CERT_TYPE } from 'views/crm/constants';
    import { encryptMobile } from 'utils/crm';

    const defaultPatientBaseInfo = {
        outpatientCount: '',
        cumulativeAmount: '',
        owePayedAmount: '',
    };

    export default {
        name: 'PatientInfoHistory',
        components: {
            CrmShebaoInfo,
            CrmPatientItemContainer,
            TagItem,
            AbcTagsAdd,
            ViewLabels,
            PatientOutpatientHistory,
            CrmPatientBasicInfo,
        },
        mixins: [
            MixinModulePermission,
        ],
        props: {
            patientInfo: {
                type: Object,
                default() {
                    return {};
                },
            },
            patientTags: {
                type: Array,
                default() {
                    return [];
                },
            },
            isChangePatientInfo: {
                type: Boolean,
                default: false,
            },
            notIdShowDetail: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                formatMoney,
                tabValue: 0,
                tags: [],
                showTagPopover: false,
                noChangeData: false,
                coverHeight: 0,
                coverTop: 0,
                postData: {},
                editor: false,
                isAddPatient: false,
                saveLoading: false,
                totalCount: 0,
                changeCascaderLoading: false,
                showLiItem: false,
                maxHeight: 0,
                visibleFamilyDoctor: false,
                visibleBindFiles: false,
                visibleCardInfo: false,
                visibleQrCode: false,
                visibleUnbindWX: false,
                visibleSocialCardInfo: false,
                loading: false,
                patientBaseInfo: Clone(defaultPatientBaseInfo),
            };
        },
        watch: {
            patientId: {
                async handler (val,oldVal) {
                    if (val !== oldVal) {
                        this.loading = true;
                        await Promise.all([
                            this.getPatientTags(),
                            this.fetchPatientBaseInfo(),
                        ]);
                        this.loading = false;
                    }
                },
                immediate: true,
            },
        },
        computed: {
            ...mapState('crm', [
                'originLabels', // 全部标签
                'crmConfigList',
            ]),
            ...mapGetters('crm', ['crmAccessVisibility']),
            ...mapGetters([
                'isOpenMp',
                'isCanSeeMedicalHistoryInRegistration',
                'isCanSeePatientMobileInRegistration',
                'chainBasic',
            ]),
            isShowHistoryFee() {
                return !!this.chainBasic.isShowHistoryFee;
            },
            isOpenSocial() {
                return this.$abcSocialSecurity.isOpenSocial;
            },
            shebaoCardInfo() {
                return this.patientInfo?.shebaoCardInfo || {};
            },
            displayWechat() {
                return this.isOpenMp && this.patientInfo?.wxBindStatus && this.patientInfo?.wxBindStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;
            },
            displayQiWei() {
                return this.patientInfo?.appFlag === 1;
            },
            displayMember() {
                return this.patientInfo?.isMember === 1;
            },
            displayCharge() {
                return this.patientInfo?.arrearsFlag === 1 && this.hasChargeModule;
            },
            patientId() {
                return this.patientInfo?.id || '';
            },
            // 患者标签展示，首先处理标签数据方便查找，再兼容id、tagId这种后端问题，得到有效的患者标签
            showTags() {
                let tags = [];
                this.originLabels.forEach((item) => {
                    if (item.tags) {
                        tags = [...tags, ...item.tags];
                    }
                });
                if (this.tags.length) {
                    return this.tags
                        .map((item) => {
                            const target = tags.find((one) => one.id === item.tagId);
                            if (target) {
                                return {
                                    ...item,
                                    tagName: target.name,
                                };
                            }
                            return false;

                        })
                        .filter((item) => item !== false);
                }
                return [];
            },
            // 已经选中的标签ids
            selectedIds() {
                return this.showTags.map((item) => item.tagId);
            },
            visitHistoryTotalCount() {
                return this.totalCount;
            },
            tabsOption() {
                const arr = [{
                    label: '患者资料',
                    value: 0,
                }];

                if (this.isCanSeeMedicalHistoryInRegistration && !this.editor) {
                    arr.push({
                        label: '就诊历史',
                        value: 1,
                        statisticsNumber: this.visitHistoryTotalCount || 0,
                    });
                }

                return arr;
            },
            isShowLiItem() {
                return this.visibleBindFiles || this.visibleFamilyDoctor || this.visibleCardInfo || this.visibleQrCode || this.visibleUnbindWX || this.visibleSocialCardInfo;
            },
            isNeedDisCharge() {
                return this.patientBaseInfo?.owePayedAmount && this.hasChargeModule;
            },
        },
        created() {
            this.$abcEventBus.$on('fetch-referrer-patient-reward', (val) => {
                this.changeCascaderLoading = val;
            }, this);

            this.$abcEventBus.$on('edit-patient-info',(emitValidateForm) => {
                this.handleEdit();
                if (emitValidateForm) {
                    this.$nextTick(() => {
                        this.$refs.crmPatientBasicInfo.onlyValidate();
                    });
                }
            }, this);
            this.$abcEventBus.$on('validate-patient-info',(callback) => {
                const result = this.validatePatientInfo();
                callback(result);
            }, this);
            this.fetchCrmConfigList();
        },
        methods: {
            ...mapActions('crm', ['fetchCrmConfigList']),
            validatePatientInfo() {
                let flag = false;
                const validateDataList = ['certificates', 'sourceInfo', 'address'];
                const {
                    address = {},
                    addressDetail = '',
                } = this.postData || {};
                for (const key in this.crmConfigList) {
                    if (this.crmConfigList?.[key].required &&
                        !validateDataList.includes(key) &&
                        !this.postData[`${key}`]) {
                        flag = true;
                    } else if (this.crmConfigList?.[key].required && validateDataList.includes(key)) {
                        if (key === 'certificates' && !this.postData.idCard) {
                            flag = true;
                        }
                        if (key === 'address' && !addressDetail) {
                            flag = true;
                        }
                        if (key === 'address' && !address.addressCityId) {
                            flag = true;
                        }
                        if (key === 'sourceInfo' && !this.postData.sourceId) {
                            flag = true;
                        }
                    }
                }
                return flag;
            },
            async getPatientTags() {
                try {
                    if (!this.patientId) {
                        this.tags = [];
                        return;
                    }
                    const { data } = await CrmAPI.fetchPatientTags(this.patientId);
                    this.tags = data || [];
                } catch (error) {
                    console.log('getPatientTags error', error);
                }
            },
            async fetchPatientBaseInfo() {
                if (!this.patientId) {
                    this.patientBaseInfo = Clone(defaultPatientBaseInfo);
                    return;
                }
                try {
                    const { data } = await CrmAPI.fetchPatientBaseInfo(this.patientId);
                    const {
                        outpatientCount = 0,
                        cumulativeAmount = 0,
                        owePayedAmount = 0,
                    } = data?.rows?.[0] || {};
                    this.patientBaseInfo.outpatientCount = outpatientCount;
                    this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                    this.patientBaseInfo.owePayedAmount = owePayedAmount;
                } catch (e) {
                    console.log(e);
                }
            },

            clickCloseLabel(tag) {
                this.clickHandleTag({
                    id: tag.tagId,
                    name: tag.tagName,
                });
            },
            async clickHandleTag(tag) {
                const id = tag.id || tag.tagId;
                const name = tag.name || tag.tagName;
                if (this.patientId) {
                    try {
                        if (this.selectedIds.includes(tag.id)) {
                            // 存在-此时删除该标签
                            await CrmAPI.deletePatientTag(this.patientId, tag.id);
                        } else {
                            // 不存在-此时打上标签
                            await CrmAPI.addPatientsTags({
                                patients: [this.patientId],
                                tags: [
                                    {
                                        tagId: id,
                                        tagName: name,
                                    },
                                ],
                            });
                        }
                        await this.getPatientTags();
                    } catch (error) {
                        console.log('clickHandleTag error', error);
                    }
                } else {
                    if (this.selectedIds.includes(id)) {
                        this.tags = this.tags.filter((item) => item.tagId !== id);
                    } else {
                        this.tags.push({
                            ...tag, tagId: id, tagName: name, editPermit: 1,
                        });
                    }
                    this.$emit('update:patientTags', this.tags);
                }
            },
            outside() {
                if (this.showTagPopover) {
                    this.showTagPopover = false;
                }
            },
            handleEdit() {
                if (!this.patientId) {
                    this.isAddPatient = true;
                }
                this.$nextTick(() => {
                    this.$refs.crmPatientBasicInfo.onClickEdit();
                });
                this.$emit('update:isChangePatientInfo', true);
            },
            handleCancel() {
                if (this.editor) {
                    if (!this.patientId) {
                        this.isAddPatient = false;
                        this.editor = false;
                    }
                    this.$refs.crmPatientBasicInfo.onClickCancel();
                }
                this.$emit('update:isChangePatientInfo', false);
            },
            handleSave() {
                if (this.saveLoading) return;
                this.$refs.crmPatientBasicInfo.validate();
            },
            async confirm() {
                this.saveLoading = true;
                try {
                    let params = clone(this.postData);
                    params = {
                        ...params,
                        ...params.address,
                        addressDetail: params.addressDetail,
                    };

                    delete params.address;

                    if (this.patientId) {
                        await CrmAPI.updatePatientInfo(this.patientId, params);
                        this.editor = false;
                        this.$Toast({
                            message: '修改成功',
                            type: 'success',
                        });
                        await this.fetchPatientOverview();
                    } else {
                        params.tags = this.patientTags || [];
                        const { data } = this.postData?.id ? await CrmAPI.updatePatientInfo(this.postData.id,params) : await CrmAPI.insertPatientInfo(params);
                        this.$Toast({
                            message: '添加成功',
                            type: 'success',
                        });
                        this.editor = false;
                        this.isAddPatient = false;
                        await this.fetchPatientOverview(data?.id);
                    }
                    this.$emit('update:isChangePatientInfo', false);
                } catch (error) {
                    console.log('onClickSave error', error);
                    if ([13502, 13503].includes(error?.code) && this.crmAccessVisibility) {
                        this.handlePatientExist(error.code, error.detail, !!this.patientId);
                    } else if (error?.code === 10409 || error?.code === 409) {
                        // 存在已经被共享的会员
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    } else if (error?.code === 13993) {
                        // 身份证被注册
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: error.message,
                        });
                    } else if (error?.code === 13992) {
                        // 该患者信息（姓名和手机号）已经被注册
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    }
                }
                this.saveLoading = false;
            },
            async fetchPatientOverview(id) {
                try {
                    const patientId = id || this.patientId;
                    if (patientId) {
                        const params = {
                            wx: 1,
                            childCareRecords: 1,
                            promotionCardList: 1,
                            showFamilyDoctor: 1,
                        };
                        const { data } = await CrmAPI.fetchPatientOverview(patientId, params);
                        const patientInfo = Object.assign({}, this.patientInfo, data);
                        this.$emit('save-patient-info-success',patientInfo);
                    }
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
            },
            handlePatientExist(code = 13503, detail = [], isModify = false) {
                const {
                    name = '',
                    mobile = '',
                    idCard = '',
                    idCardType = DEFAULT_CERT_TYPE,
                    id = '',
                } = detail?.[0] || {};
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    confirmText: isModify ? '添加患者' : '使用患者',
                    showCancel: false,
                    content: code === 13502 ? `证件号[${idCardType}]${idCard}已经被连锁中存在的患者 ${name} 注册` : `同名患者 ${name} ${mobile ? `${this.isCanSeePatientMobileInCrm ? mobile : encryptMobile(mobile)}` : ''} ${idCard ? `[${idCardType}]${idCard}` : ''}已在连锁中存在`,
                    onConfirm: async () => {
                        await this.addPatientFromOtherClinic(id, isModify);
                    },
                });
            },
            // 从其他门店添加患者
            async addPatientFromOtherClinic(id, isModify = false) {
                try {
                    await CrmAPI.handlePatientExist(id);
                    if (isModify) {
                        this.$Toast({
                            message: '添加患者成功',
                            type: 'success',
                        });
                    } else {
                        this.$Toast({
                            message: '使用患者成功',
                            type: 'success',
                        });
                        this.editor = false;
                        this.isAddPatient = false;
                        this.$emit('add-and-select-patient', id);
                        this.$emit('update:isChangePatientInfo', false);
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            handleCommon() {
                this.showLiItem = true;
                const {
                    clientHeight,
                    offsetHeight,
                    scrollTop,
                } = this.$refs.contentWrapper;
                this.maxHeight = clientHeight;
                this.coverHeight = offsetHeight;
                this.coverTop = scrollTop;
            },
            handleTotalCount(val) {
                this.totalCount = val;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import 'src/styles/mixin';

.patient-info-history-wrapper {
    padding: 4px 0;
    background-color: var(--abc-color-cp-grey2);
    border-radius: var(--abc-border-radius-mini);

    &.edit-status {
        background-color: $S2;
    }

    &.display-history-wrapper {
        .content {
            padding: 0 0 12px 0;
        }
    }

    .content-wrapper {
        position: relative;
        max-height: 597px;
        overflow-y: auto;

        &.overflow-y-hidden {
            overflow-y: hidden;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        padding: 10px 16px;

        .left {
            .abc-tabs {
                height: 24px;
                line-height: 24px;

                .abc-tabs-item-content-wrapper {
                    font-size: 13px;

                    .abc-tabs-dot {
                        font-size: 13px !important;
                    }
                }
            }
        }

        .right {
            .abc-button {
                height: 24px;
                min-height: 24px;
                font-size: 13px;
            }
        }
    }

    .display-split-line {
        height: 1px;
        background-color: $P6;
    }

    .content {
        padding: 0 16px 12px;

        .outpatient-statistics-wrapper {
            margin: 4px 0 7px 0;
            font-size: 13px;

            .owe-pay {
                color: var(--abc-color-R6);
            }
        }

        .patient-info-wrapper {
            &.is-editor-status {
                margin-top: 12px;
            }

            .crm-patient-item-container-wrapper {
                margin-top: var(--abc-paddingTB-l);
            }

            .crm-patient-item-container-wrapper-shebao {
                margin-top: var(--abc-paddingTB-xl);
            }
        }

        .cover {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            width: 100%;
            height: 100vh;
            background: $T1;
            border-radius: var(--abc-border-radius-mini);
            opacity: 0.3;
        }

        .patient-info-dialog-wrapper {
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 9999999;
            box-sizing: border-box;
            width: calc(100% - 24px);
            height: auto;
            background: $S2;
            border-radius: var(--abc-border-radius-mini);
            transform: translate(-50%, -50%);

            &.dialog-wx-box {
                width: 183px;
                height: 211px;
            }
        }

        .tag-wrapper {
            &_logo {
                width: 20px;
                height: 20px;
                margin-right: 6px;
                margin-bottom: 8px;
            }
        }

        .abc-tag-group-wrapper {
            margin: 14px 0 -3px;

            .tag-item-wrapper {
                .abc-tag-wrapper {
                    margin-right: 6px;
                }
            }
        }

        .outpatient-history-list {
            min-height: 276px;
            max-height: 336px;

            .history-item-wrapper {
                .outpatient-detail {
                    .content-section {
                        &:first-child {
                            padding-top: 0;
                        }
                    }
                }
            }
        }
    }

    .patient-info-tab {
        .cover-wrap {
            border-radius: var(--abc-border-radius-medium);
        }
    }
}
</style>
